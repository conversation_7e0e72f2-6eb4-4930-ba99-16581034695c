{"server1": ["<PERSON><PERSON><PERSON>"], "server2": [], "relayServers": [], "orgs": {"Bisha": ["BishaTime", "BishaMillSlicer", "BishaAnalytic", "BishaExcel"]}, "opServerOptions": {"hostname": "*************", "port": 8989, "path": "/api/pyomo/", "method": "POST", "headers": {"Content-Type": "application/json"}, "rejectUnauthorized": false}, "servers": {"BishaTime": {"type": "influx", "host": "******************************************", "readPort": 8086, "writePort": 8091, "database": "PIQ_Bisha_IOT_Database", "table": "process_data"}, "BishaMillSlicer": {"type": "influx", "host": "******************************************", "readPort": 8086, "writePort": 8091, "database": "PIQ_Bisha_IOT_Database", "table": "process_data"}, "BishaAnalytic": {"type": "influx", "host": "******************************************", "readPort": 8086, "writePort": 8091, "database": "PIQ_Bisha_IOT_Database", "table": "process_data"}, "BishaExcel": {"type": "influx", "host": "******************************************", "readPort": 8086, "writePort": 8091, "database": "PIQ_Bisha_IOT_Database", "table": "process_data"}}}