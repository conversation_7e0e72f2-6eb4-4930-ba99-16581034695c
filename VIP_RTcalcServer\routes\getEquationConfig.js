var FormData = require('form-data');
const path = require('path');
var express = require('express');
var moment = require("moment");
var router = express.Router();
var zip = require('express-zip');
var fs = require('fs');
var util = require('util');
var jszip = require("jszip");
const archiver = require('archiver')
var multer = require('multer');
var upload = multer();
var formidable = require('formidable');
var os = require('os');
const http = require('http')
var serverName = require('./../../serverName.json');
var config = require('./../../config.json');

var calcSetupFolder =  __dirname+'/../../liveConfigs';
var configVersionsFolder =  __dirname+'/../../rtCalcConfigArchive';
var archiverTemp =  __dirname+'/../../archiverTemp';
var syncConfigFolder = __dirname+'/../../syncSettings';
var syncConfigLoc = __dirname+'/../../syncSettings/syncConfig.json';
var relayServers = []
var exec = require('child_process').exec

if (config["relayServers"] != undefined) { // need to create serverName.json file in root directory with {name://unique server name string//} 
    relayServers = config["relayServers"]
}   

router.get('/', function (req, res) {
    console.log('getEquationconfig.js GET');
    try {
        site = req.query.site.replace(/\s+/g, '')
        fileName = site+"_"+moment().unix().toString()
        reqFolderDir = calcSetupFolder+"/"+site
        resZip = archiverTemp + '/'+fileName+'.zip'
        var syncConfig = require(syncConfigLoc)

        if (!fs.existsSync(archiverTemp)) {
            fs.mkdirSync(archiverTemp)
        }

        if (fs.existsSync(reqFolderDir)) {
            const output = fs.createWriteStream(resZip);
            const archive = archiver('zip', {
              zlib: { level: 9 }
            });

            archive.on('warning', function(err) {
              if (err.code === 'ENOENT') {
                console.log("Warning: "+err)
              }

            });
            archive.on('error', function(err) {
              console.log("Error: "+err)
            });

            output.on('close', function() {
                console.log(archive.pointer() + ' total bytes');
                console.log('archiver has been finalized and the output file descriptor has closed.');
                res.sendFile(path.resolve(resZip))
                res.on('finish', function () {
                    res.end()
                    output.end()
                    fs.unlink(resZip, (err => {
                        if (err) { console.log(err)}
                    }))
                })
            });

            archive.pipe(output);

            if (syncConfig[site] != undefined) {
                for (let file of syncConfig[site]["syncDashboards"]) {
                    if (fs.existsSync(reqFolderDir+"/"+file)) {
                        archive.append(fs.createReadStream(reqFolderDir+"/"+file), { name: file});
                    }
                }
            }

            archive.finalize()
        } else {
            console.log(site+" configurations not found on the server")
        }
    }
    catch (e) {
        console.log('ERROR: getEquationconfig.js GET - ' + e.message);
    }
});

router.post('/', function (req, res) {
    console.log('getEquationconfig.js POST');
    try {
        var form = new formidable.IncomingForm(),
            files = [],
            fields = {};

        form.uploadDir = os.tmpdir();
        form.on('field', function (field, value) {
            fields[field] = value;
        }).on('file', function (field, file) {
            files.push([field, file]);
        }).on('end', function () {
                console.log('Upload request received');
                let error = false;

                files.forEach(function (element) {
                    console.log('file');

                    fs.readFile(element[1].path, function (err, data) {
                        console.log(element[1].path);
                        if (err) {
                            error = true;
                            console.error("ERROR!", err);
                        } else {
                            if (relayServers.includes(serverName.name)) {
                                try {
                                    let dir = os.tmpdir()+"/"+"toUpload2_"+Math.random().toString(36).slice(2);
                                    fs.writeFile(dir, data, function () {
                                        var formData = new FormData();
                                        formData.append('toRTCalc', fs.createReadStream(dir));
                                        var options = {
                                          host: '*************',
                                          port:  85,
                                          path: '/getEquationConfig',
                                          method: 'POST',
                                          headers: formData.getHeaders()
                                        }


                                        const relay = http.request(options, res => {
                                          console.log(`statusCode: ${res.statusCode} for file relayed`)

                                          res.on('data', d => {
                                            console.log("message returned from upload server 2"+d)
                                          })
                                        })

                                        formData.pipe(relay)
                                    })
                                } catch (e) {
                                    console.log(e)
                                }
                                try {
                                    let dir2 = os.tmpdir()+"/"+"toUpload3_"+Math.random().toString(36).slice(2);
                                    fs.writeFile(dir2, data, function () {
                                        var formData = new FormData();
                                        formData.append('toRTCalc', fs.createReadStream(dir2));
                                        var options = {
                                          host: '*************',
                                          port:  85,
                                          path: '/getEquationConfig',
                                          method: 'POST',
                                          headers: formData.getHeaders()
                                        }


                                        const relay = http.request(options, res => {
                                          console.log(`statusCode: ${res.statusCode} for file relayed`)

                                          res.on('data', d => {
                                            console.log("message returned from upload server 3"+d)
                                          })
                                        })

                                        formData.pipe(relay)
                                    })
                                } catch (e) {
                                    console.log(e)
                                }
                            }

                            jszip.loadAsync(data).then(function (zip) {
                                files = Object.keys(zip.files);
                                for (i = 0; i < files.length; i++) {
                                    zip.file(files[i]).async("string").then(function (data) {

                                        let parsedData = JSON.parse(data)
                                        let site = parsedData["Site"];
                                        let siteNoSpace = site.replace(/\s+/g, '')
                                        let pageName = parsedData["Dashboard"];
                                        let pageNameNoSpace = pageName.replace(/\s+/g, '')
                                        let pageID = parsedData["DashboardID"]
                                        let fileName = pageNameNoSpace + "." + pageID

                                        // Archive Directory
                                        let siteArchiveDirectory = configVersionsFolder+'/'+siteNoSpace
                                        if (!(fs.existsSync(siteArchiveDirectory))) {
                                            fs.mkdirSync(siteArchiveDirectory);
                                        }

                                        let pageArchiveDirectory = siteArchiveDirectory +'/'+fileName
                                        if (!(fs.existsSync(pageArchiveDirectory))) {
                                            fs.mkdirSync(pageArchiveDirectory);
                                        }

                                        fs.appendFile(pageArchiveDirectory+'/'+moment().unix().toString(), data, function (err) {
                                          if (err) console.log("ERROR create file ", err);
                                        });
                                        
                                        // Live dashboard directory
                                        let siteDirectory = calcSetupFolder+'/'+siteNoSpace
                                        if (!(fs.existsSync(siteDirectory))) {
                                            fs.mkdirSync(siteDirectory);
                                        }

                                        let filePath = siteDirectory+'/'+fileName+".json"
                                        if(fs.existsSync(filePath)){
                                            fs.writeFile(filePath, data, function(err){
                                                if (err) console.error("ERROR write file ", err);
                                            })
                                        } else {
                                            fs.readdirSync(siteDirectory).forEach(fileName => {
                                                let parts = fileName.split(".")
                                                if(parts.length == 3){
                                                    let name = parts[0], id = parts[1]
                                                    if(name == pageNameNoSpace || id == pageID){
                                                        fs.unlinkSync(siteDirectory+"/"+fileName)
                                                    }
                                                }
                                            });
                                            fs.writeFile(filePath, data, function(err){
                                                if (err) console.error("ERROR write file ", err);
                                            })
                                        }
                                        
                                        req.app.locals.configsMap.set(site+pageNameNoSpace, parsedData);

                                    }).catch((err) => {
                                        error = true;
                                        console.error("ERROR (zip.file)", err);
                                    });
                                }
                            }).catch((err) => {
                                error = true;
                                console.error("ERROR (jszip.loadAsync)", err);
                            });
                        }
                    });
                });

                res.writeHead(error ? 400 : 200, { 'content-type': 'text/plain', 'Access-Control-Allow-Origin': '*' });
                res.end('received files:\n\n ' + util.inspect(files));
            });

        form.parse(req);
    }
    catch (e) {
        console.log('ERROR: getEquationconfig.js POST - ' + e.message);
    }
});

module.exports = router;
