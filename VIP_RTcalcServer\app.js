﻿var debug = require('debug');
var express = require('express');
var path = require('path');
var favicon = require('serve-favicon');
var logger = require('morgan');
var cookieParser = require('cookie-parser');
var bodyParser = require('body-parser');

var routes = require('./routes/index');
var users = require('./routes/users');
var getEquationConfig = require('./routes/getEquationConfig');
var getEqSettings = require('./routes/getEqSettings');
var findEq = require('./routes/findEq');
var writeEquationConfig = require('./routes/writeEquationConfig');
var runEquationConfig = require('./routes/runEquationConfig');
var app = express();

// view engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'pug');

// uncomment after placing your favicon in /public
//app.use(favicon(__dirname + '/public/favicon.ico'));
app.use(logger('dev'));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: false }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));

app.use('/', routes);
app.use('/users', users);
app.use('/getEquationConfig', getEquationConfig); // Upload to rt calc
app.use('/getEqSettings', getEqSettings); // settings for dashbaords whether to sync or not etc
app.use('/findEq', findEq); // to find dashboard with that equation
app.use('/writeEquationConfig', writeEquationConfig); // Save to DB
app.use('/runEquationConfig', runEquationConfig); // APPLY
// catch 404 and forward to error handler
app.use(function (req, res, next) {
    var err = new Error('Not Found');
    err.status = 404;
    next(err);
});

// error handlers

// development error handler
// will print stacktrace
if (app.get('env') === 'development') {
    app.use(function (err, req, res, next) {
        res.status(err.status || 500);
        res.render('error', {
            message: err.message,
            error: err
        });
    });
}

// production error handler
// no stacktraces leaked to user
app.use(function (err, req, res, next) {
    res.status(err.status || 500);
    res.render('error', {
        message: err.message,
        error: {}
    });
});

module.exports = app;