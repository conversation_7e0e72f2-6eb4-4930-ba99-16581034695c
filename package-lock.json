{"name": "send-message", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "send-message", "version": "1.0.0", "license": "MIT", "dependencies": {"form-data": "^4.0.0", "node-opcua": "^2.81.0"}, "devDependencies": {"archiver": "^3.1.1", "aws-sdk": "^2.739.0", "body-parser": "^1.15.0", "cookie-parser": "^1.4.0", "debug": "^2.2.0", "express": "^4.14.0", "express-zip": "^2.0.1", "formidable": "^1.2.1", "https": "^1.0.0", "influx": "^5.4.1", "jszip": "^3.2.2", "mathjs": "^6.2.2", "modbusjs": "^0.0.11", "moment": "^2.24.0", "morgan": "^1.7.0", "mqtt": "^3.0.0", "multer": "^1.4.2", "mysql": "^2.17.1", "mysql2": "^1.7.0", "pug": "^2.0.0-beta6", "serve-favicon": "^2.3.0", "set-interval-async": "^1.0.29"}}, "node_modules/@babel/runtime": {"version": "7.5.0", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.5.0.tgz", "integrity": "sha512-2xsuyZ0R0RBFwjgae5NpXk8FcfH4qovj5cEM5VEeB7KXnKqzaisIu2HSV/mCEISolJJuR4wkViUGYujA8MH9tw==", "dev": true, "dependencies": {"regenerator-runtime": "^0.13.2"}}, "node_modules/@babel/runtime/node_modules/regenerator-runtime": {"version": "0.13.3", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.3.tgz", "integrity": "sha512-naKIZz2GQ8JWh///G7L3X6LaQUAMp2lvb1rvwwsURe/VXwD6VMfr+/1NuNw3ag8v2kY1aQ/go5SNn79O9JU7yw==", "dev": true}, "node_modules/@leichtgewicht/ip-codec": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/@leichtgewicht/ip-codec/-/ip-codec-2.0.4.tgz", "integrity": "sha512-Hcv+nVC0kZnQ3tD9GVu5xSMR4VVYOteQIr/hwFPVEvPdlXqgGEuRjiheChHgdM+JyqdgNcmzZOX/tnl0JOiI7A=="}, "node_modules/@ster5/global-mutex": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@ster5/global-mutex/-/global-mutex-1.2.0.tgz", "integrity": "sha512-UmLDijFT0oif4HNuHkXGEolwQe3Eb5x1WX+1fxEMrO92BojeDycn4gjQnZm8d6nZPizzbWrt6SLQgDU+bFALyw=="}, "node_modules/@types/async": {"version": "3.2.15", "resolved": "https://registry.npmjs.org/@types/async/-/async-3.2.15.tgz", "integrity": "sha512-PAmPfzvFA31mRoqZyTVsgJMsvbynR429UTTxhmfsUCrWGh3/fxOrzqBtaTPJsn4UtzTv4Vb0+/O7CARWb69N4g=="}, "node_modules/@types/babel-types": {"version": "7.0.7", "resolved": "https://registry.npmjs.org/@types/babel-types/-/babel-types-7.0.7.tgz", "integrity": "sha512-dBtBbrc+qTHy1WdfHYjBwRln4+LWqASWakLHsWHR2NWHIFkv4W3O070IGoGLEBrJBvct3r0L1BUPuvURi7kYUQ==", "dev": true}, "node_modules/@types/babylon": {"version": "6.16.5", "resolved": "https://registry.npmjs.org/@types/babylon/-/babylon-6.16.5.tgz", "integrity": "sha512-xH2e58elpj1X4ynnKp9qSnWlsRTIs6n3tgLGNfwAGHwePw0mulHQllV34n0T25uYSu1k0hRKkWXF890B1yS47w==", "dev": true, "dependencies": {"@types/babel-types": "*"}}, "node_modules/@types/dns-packet": {"version": "5.2.4", "resolved": "https://registry.npmjs.org/@types/dns-packet/-/dns-packet-5.2.4.tgz", "integrity": "sha512-OAruArypdNxR/tzbmrtoyEuXeNTLaZCpO19BXaNC10T5ACIbvjmvhmV2RDEy2eLc3w8IjK7SY3cvUCcAW+sfoQ==", "dependencies": {"@types/node": "*"}}, "node_modules/@types/lodash": {"version": "4.14.185", "resolved": "https://registry.npmjs.org/@types/lodash/-/lodash-4.14.185.tgz", "integrity": "sha512-evMDG1bC4rgQg4ku9tKpuMh5iBNEwNa3tf9zRHdP1qlv+1WUg44xat4IxCE14gIpZRGUUWAx2VhItCZc25NfMA=="}, "node_modules/@types/mkdirp": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@types/mkdirp/-/mkdirp-1.0.2.tgz", "integrity": "sha512-o0K1tSO0Dx5X6xlU5F1D6625FawhC3dU3iqr25lluNv/+/QIVH8RLNEiVokgIZo+mz+87w/3Mkg/VvQS+J51fQ==", "dependencies": {"@types/node": "*"}}, "node_modules/@types/multicast-dns": {"version": "7.2.1", "resolved": "https://registry.npmjs.org/@types/multicast-dns/-/multicast-dns-7.2.1.tgz", "integrity": "sha512-A2PmB8MRcNVEkw6wzGT5rtBHqyHOVjiRMkJH+zpJKXipSi+GGkHg6JjNFApDiYK9WefJqkVG0taln1VMl4TGfw==", "dependencies": {"@types/dns-packet": "*", "@types/node": "*"}}, "node_modules/@types/node": {"version": "18.11.0", "resolved": "https://registry.npmjs.org/@types/node/-/node-18.11.0.tgz", "integrity": "sha512-IOXCvVRToe7e0ny7HpT/X9Rb2RYtElG1a+VshjwT00HxrM2dWBApHQoqsI6WiY7Q03vdf2bCrIGzVrkF/5t10w=="}, "node_modules/@types/once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@types/once/-/once-1.4.0.tgz", "integrity": "sha512-cnEvTAVVRqF6OQg/4SLnbxQ0slZJHqZQDve5BzGhcIQtuMpPv8T5QNS2cBPa/W0jTxciqwn7bmJAIGe/bOJ5Kw=="}, "node_modules/@types/semver": {"version": "7.3.12", "resolved": "https://registry.npmjs.org/@types/semver/-/semver-7.3.12.tgz", "integrity": "sha512-WwA1MW0++RfXmCr12xeYOOC5baSC9mSb0ZqCquFzKhcoF4TvHu5MKOuXsncgZcpVFhB1pXd5hZmM0ryAoCp12A=="}, "node_modules/@types/underscore": {"version": "1.11.4", "resolved": "https://registry.npmjs.org/@types/underscore/-/underscore-1.11.4.tgz", "integrity": "sha512-uO4CD2ELOjw8tasUrAhvnn2W4A0ZECOvMjCivJr4gA9pGgjv+qxKWY9GLTMVEK8ej85BxQOocUyE7hImmSQYcg=="}, "node_modules/accepts": {"version": "1.3.7", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.7.tgz", "integrity": "sha512-Il80Qs2WjYlJIBNzNkK6KYqlVMTbZLXgHx2oT0pU/fjRHyEp+PEfEPY0R3WCwAGVOtauxh1hOxNgIf5bv7dQpA==", "dev": true, "dependencies": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/acorn/-/acorn-3.3.0.tgz", "integrity": "sha1-ReN/s56No/JbruP/U2niu18iAXo=", "dev": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-globals": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-3.1.0.tgz", "integrity": "sha1-/YJw9x+7SZawBPqIDuXUZXOnMb8=", "dev": true, "dependencies": {"acorn": "^4.0.4"}}, "node_modules/acorn-globals/node_modules/acorn": {"version": "4.0.13", "resolved": "https://registry.npmjs.org/acorn/-/acorn-4.0.13.tgz", "integrity": "sha1-EFSVrlNh1pe9GVyCUZLhrX8lN4c=", "dev": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/align-text": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/align-text/-/align-text-0.1.4.tgz", "integrity": "sha1-DNkKVhCT810KmSVsIrcGlDP60Rc=", "dev": true, "dependencies": {"kind-of": "^3.0.2", "longest": "^1.0.1", "repeat-string": "^1.5.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/any-promise": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz", "integrity": "sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A=="}, "node_modules/anymatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.2.tgz", "integrity": "sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/append-field": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/append-field/-/append-field-1.0.0.tgz", "integrity": "sha1-HjRA6RXwsSA9I3SOeO3XubW0PlY=", "dev": true}, "node_modules/archiver": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/archiver/-/archiver-3.1.1.tgz", "integrity": "sha512-5Hxxcig7gw5Jod/8Gq0OneVgLYET+oNHcxgWItq4TbhOzRLKNAFUb9edAftiMKXvXfCB0vbGrJdZDNq0dWMsxg==", "dev": true, "dependencies": {"archiver-utils": "^2.1.0", "async": "^2.6.3", "buffer-crc32": "^0.2.1", "glob": "^7.1.4", "readable-stream": "^3.4.0", "tar-stream": "^2.1.0", "zip-stream": "^2.1.2"}, "engines": {"node": ">= 6"}}, "node_modules/archiver-utils": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/archiver-utils/-/archiver-utils-2.1.0.tgz", "integrity": "sha512-bEL/yUb/fNNiNTuUz979Z0Yg5L+LzLxGJz8x79lYmR54fmTIb6ob/hNQgkQnIUDWIFjZVQwl9Xs356I6BAMHfw==", "dev": true, "dependencies": {"glob": "^7.1.4", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^2.0.0"}, "engines": {"node": ">= 6"}}, "node_modules/archiver/node_modules/async": {"version": "2.6.3", "resolved": "https://registry.npmjs.org/async/-/async-2.6.3.tgz", "integrity": "sha512-zflvls11DCy+dQWzTW2dzuilv8Z5X/pjfmZOWba6TNIVDm+2UDaJmXSOXlasHKfNBs8oo3M0aT50fDEWfKZjXg==", "dev": true, "dependencies": {"lodash": "^4.17.14"}}, "node_modules/archiver/node_modules/compress-commons": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/compress-commons/-/compress-commons-2.1.1.tgz", "integrity": "sha512-eVw6n7CnEMFzc3duyFVrQEuY1BlHR3rYsSztyG32ibGMW722i3C6IizEGMFmfMU+A+fALvBIwxN3czffTcdA+Q==", "dev": true, "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^3.0.1", "normalize-path": "^3.0.0", "readable-stream": "^2.3.6"}, "engines": {"node": ">= 6"}}, "node_modules/archiver/node_modules/compress-commons/node_modules/readable-stream": {"version": "2.3.6", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw==", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/archiver/node_modules/crc32-stream": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/crc32-stream/-/crc32-stream-3.0.1.tgz", "integrity": "sha512-mctvpXlbzsvK+6z8kJwSJ5crm7yBwrQMTybJzMw1O4lLGJqjlDCXY2Zw7KheiA6XBEcBmfLx1D88mjRGVJtY9w==", "dev": true, "dependencies": {"crc": "^3.4.4", "readable-stream": "^3.4.0"}, "engines": {"node": ">= 6.9.0"}}, "node_modules/archiver/node_modules/readable-stream": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.4.0.tgz", "integrity": "sha512-jItXPLmrSR8jmTRmRWJXCnGJsfy85mB3Wd/uINMXA65yrnFo0cPClFIUWzo2najVNSl+mx7/4W8ttlLWJe99pQ==", "dev": true, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/archiver/node_modules/zip-stream": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/zip-stream/-/zip-stream-2.1.2.tgz", "integrity": "sha512-ykebHGa2+uzth/R4HZLkZh3XFJzivhVsjJt8bN3GvBzLaqqrUdRacu+c4QtnUgjkkQfsOuNE1JgLKMCPNmkKgg==", "dev": true, "dependencies": {"archiver-utils": "^2.1.0", "compress-commons": "^2.1.1", "readable-stream": "^3.4.0"}, "engines": {"node": ">= 6"}}, "node_modules/array-flatten": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=", "dev": true}, "node_modules/asap": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz", "integrity": "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=", "dev": true}, "node_modules/asn1": {"version": "0.2.6", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.2.6.tgz", "integrity": "sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "engines": {"node": ">=0.8"}}, "node_modules/async": {"version": "0.9.0", "resolved": "https://registry.npmjs.org/async/-/async-0.9.0.tgz", "integrity": "sha1-rDYTsdqb7RtHUQu0ZRuJMeRxRsc=", "dev": true}, "node_modules/async-limiter": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/async-limiter/-/async-limiter-1.0.1.tgz", "integrity": "sha512-csOlWGAcRFJaI6m+F2WKdnMKr4HhdhFVBk0H/QbJFMCr+uO2kwohwXQPxw/9OCxp05r5ghVBFSyioixx3gfkNQ==", "dev": true}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k="}, "node_modules/aws-sdk": {"version": "2.997.0", "resolved": "https://registry.npmjs.org/aws-sdk/-/aws-sdk-2.997.0.tgz", "integrity": "sha512-PiuDmC5hN+FsyLvl7GsZAnS6hQpo1pP+Ax2u8gyL19QlbBLwlhsFQF29vPcYatyv6WUxr51o6uymJdPxQg6uEA==", "dev": true, "hasInstallScript": true, "dependencies": {"buffer": "4.9.2", "events": "1.1.1", "ieee754": "1.1.13", "jmespath": "0.15.0", "querystring": "0.2.0", "sax": "1.2.1", "url": "0.10.3", "uuid": "3.3.2", "xml2js": "0.4.19"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/aws-sdk/node_modules/buffer": {"version": "4.9.2", "resolved": "https://registry.npmjs.org/buffer/-/buffer-4.9.2.tgz", "integrity": "sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==", "dev": true, "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}}, "node_modules/babel-runtime": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.26.0.tgz", "integrity": "sha1-llxwWGaOgrVde/4E/yM3vItWR/4=", "dev": true, "dependencies": {"core-js": "^2.4.0", "regenerator-runtime": "^0.11.0"}}, "node_modules/babel-types": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-types/-/babel-types-6.26.0.tgz", "integrity": "sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc=", "dev": true, "dependencies": {"babel-runtime": "^6.26.0", "esutils": "^2.0.2", "lodash": "^4.17.4", "to-fast-properties": "^1.0.3"}}, "node_modules/babylon": {"version": "6.18.0", "resolved": "https://registry.npmjs.org/babylon/-/babylon-6.18.0.tgz", "integrity": "sha512-q/UEjfGJ2Cm3oKV71DJz9d25TPnq5rhBVL2Q4fA5wcC3jcrdn7+SssEybFIxwAvvP+YCsCYNKughoF33GxgycQ==", "dev": true, "bin": {"babylon": "bin/babylon.js"}}, "node_modules/backoff": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/backoff/-/backoff-2.5.0.tgz", "integrity": "sha512-wC5ihrnUXmR2douXmXLCe5O3zg3GKIyvRi/hi58a/XyRxVI+3/yM0PYueQOZXPXQ9pxBislYkw+sF9b7C/RuMA==", "dependencies": {"precond": "0.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/balanced-match": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c=", "dev": true}, "node_modules/base64-js": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.3.1.tgz", "integrity": "sha512-mLQ4i2QO1ytvGWFWmcngKO//JXAQueZvwEKtjgQFM4jIK0kU+ytMfplL8j+n5mspOfjHwoAg+9yhb7BwAHm36g==", "dev": true}, "node_modules/basic-auth": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/basic-auth/-/basic-auth-2.0.1.tgz", "integrity": "sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==", "dev": true, "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.8"}}, "node_modules/bcrypt-pbkdf": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==", "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/better-assert": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/better-assert/-/better-assert-1.0.2.tgz", "integrity": "sha512-bY<PERSON><PERSON>2DFlpK1XmGs6fvlLRUN29QISM3GBuUwSFsMY2XRx4AvC0WNCS57j4c/xGrK2RS24C1w3YoBOsw9fT46tQ==", "dependencies": {"callsite": "1.0.0"}, "engines": {"node": "*"}}, "node_modules/bignumber.js": {"version": "7.2.1", "resolved": "https://registry.npmjs.org/bignumber.js/-/bignumber.js-7.2.1.tgz", "integrity": "sha512-S4XzBk5sMB+Rcb/LNcpzXr57VRTxgAvaAEDAl1AwRx27j00hT84O6OkteE7u8UB3NuaaygCRrEpqox4uDOrbdQ==", "dev": true, "engines": {"node": "*"}}, "node_modules/binary-extensions": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.2.0.tgz", "integrity": "sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==", "engines": {"node": ">=8"}}, "node_modules/bl": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/bl/-/bl-1.2.2.tgz", "integrity": "sha512-e8tQYnZodmebYDWGH7KMRvtzKXaJHx3BbilrgZCfvyLUYdKpK1t5PSPmpkny/SgiTSCnjfLW7v5rlONXVFkQEA==", "dev": true, "dependencies": {"readable-stream": "^2.3.5", "safe-buffer": "^5.1.1"}}, "node_modules/body-parser": {"version": "1.19.0", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.19.0.tgz", "integrity": "sha512-dhEPs72UPbDnAQJ9ZKMNTP6ptJaionhP5cBb541nXPlW60Jepo9RV/a4fX4XWW9CuFNK22krhrj1+rgzifNCsw==", "dev": true, "dependencies": {"bytes": "3.1.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "on-finished": "~2.3.0", "qs": "6.7.0", "raw-body": "2.4.0", "type-is": "~1.6.17"}, "engines": {"node": ">= 0.8"}}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz", "integrity": "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==", "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/buffer": {"version": "5.4.3", "resolved": "https://registry.npmjs.org/buffer/-/buffer-5.4.3.tgz", "integrity": "sha512-zvj65TkFeIt3i6aj5bIvJDzjjQQGs4o/sNoezg1F1kYap9Nu2jcUdpwzRSJTHMMzG0H7bZkn4rNQpImhuxWX2A==", "dev": true, "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}}, "node_modules/buffer-crc32": {"version": "0.2.13", "resolved": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "integrity": "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=", "engines": {"node": "*"}}, "node_modules/buffer-from": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.1.tgz", "integrity": "sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A==", "dev": true}, "node_modules/busboy": {"version": "0.2.14", "resolved": "https://registry.npmjs.org/busboy/-/busboy-0.2.14.tgz", "integrity": "sha1-bCpiLvz0fFe7vh4qnDetNseSVFM=", "dev": true, "dependencies": {"dicer": "0.2.5", "readable-stream": "1.1.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/busboy/node_modules/isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "node_modules/busboy/node_modules/readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha1-fPTFTvZI44EwhMY23SB54WbAgdk=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/busboy/node_modules/string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=", "dev": true}, "node_modules/byline": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/byline/-/byline-5.0.0.tgz", "integrity": "sha512-s6webAy+R4SR8XVuJWt2V2rGvhnrhxN+9S15GNuTK3wKPOXFF6RNc+8ug2XhH+2s4f+uudG4kUVYmYOQWL2g0Q==", "engines": {"node": ">=0.10.0"}}, "node_modules/bytes": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-3.1.0.tgz", "integrity": "sha512-zauLjrfCG+xvoyaqLoV8bLVXXNGC4JqlxFCutSDWA6fJrTo2ZuvLYTqZ7aHBLZSMOopbzwv8f+wZcVzfVTI2Dg==", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/callback-stream": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/callback-stream/-/callback-stream-1.1.0.tgz", "integrity": "sha1-RwGlEmbwbgbqpx/BcjOCLYdfSQg=", "dev": true, "dependencies": {"inherits": "^2.0.1", "readable-stream": "> 1.0.0 < 3.0.0"}}, "node_modules/callbackify": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/callbackify/-/callbackify-1.1.0.tgz", "integrity": "sha512-dypYUxqOLco4orSE0+0DcMkdBNHGT11/bNdQkLVtq3sWdShwtgdet18BgAoBHPZpDK2WfisIWJpYAFV321+Jtw=="}, "node_modules/callsite": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/callsite/-/callsite-1.0.0.tgz", "integrity": "sha512-0vdNRFXn5q+dtOqjfFtmtlI9N2eVZ7LMyEV2iKC5mEEFvSg/69Ml6b/WU2qF8W1nLRa0wiSrDT3Y5jOHZCwKPQ==", "engines": {"node": "*"}}, "node_modules/camelcase": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz", "integrity": "sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/center-align": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/center-align/-/center-align-0.1.3.tgz", "integrity": "sha1-qg0yYptu6XIgBBHL1EYckHvCt60=", "dev": true, "dependencies": {"align-text": "^0.1.3", "lazy-cache": "^1.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/character-parser": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/character-parser/-/character-parser-2.2.0.tgz", "integrity": "sha1-x84o821LzZdE5f/CxfzeHHMmH8A=", "dev": true, "dependencies": {"is-regex": "^1.0.3"}}, "node_modules/chokidar": {"version": "3.5.3", "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-3.5.3.tgz", "integrity": "sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==", "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chokidar/node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/chokidar/node_modules/is-glob": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/clean-css": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/clean-css/-/clean-css-4.2.1.tgz", "integrity": "sha512-4ZxI6dy4lrY6FHzfiy1aEOXgu4LIsW2MhwG0VBKdcoGoH/XLFgaHSdLTGr4O8Be6A8r3MOphEiI8Gc1n0ecf3g==", "dev": true, "dependencies": {"source-map": "~0.6.0"}, "engines": {"node": ">= 4.0"}}, "node_modules/cli-table": {"version": "0.3.11", "resolved": "https://registry.npmjs.org/cli-table/-/cli-table-0.3.11.tgz", "integrity": "sha512-IqLQi4lO0nIB4tcdTpN4LCB9FI3uqrJZK7RC515EnhZ6qBaglkIgICb1wjeAqpdoOabm1+SuQtkXIPdYC93jhQ==", "dependencies": {"colors": "1.0.3"}, "engines": {"node": ">= 0.2.0"}}, "node_modules/cliui": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/cliui/-/cliui-2.1.0.tgz", "integrity": "sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE=", "dev": true, "dependencies": {"center-align": "^0.1.1", "right-align": "^0.1.1", "wordwrap": "0.0.2"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="}, "node_modules/colors": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/colors/-/colors-1.0.3.tgz", "integrity": "sha512-pFGrxThWcWQ2MsAz6RtgeWe4NK2kUE1WfsrvvlctdII745EW9I0yflqhe7++M5LEc7bV2c/9/5zc8sFcpL0Drw==", "engines": {"node": ">=0.1.90"}}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commist": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/commist/-/commist-1.1.0.tgz", "integrity": "sha512-rraC8NXWOEjhADbZe9QBNzLAN5Q3fsTPQtBV+fEVj6xKIgDgNiEVE6ZNfHpZOqfQ21YUzfVNUXLOEZquYvQPPg==", "dev": true, "dependencies": {"leven": "^2.1.0", "minimist": "^1.1.0"}}, "node_modules/complex.js": {"version": "2.0.11", "resolved": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.11.tgz", "integrity": "sha512-6IArJLApNtdg1P1dFtn3dnyzoZBEF0MwMnrfF1exSBRpZYoy4yieMkpZhQDC0uwctw48vii0CFVyHfpgZ/DfGw==", "dev": true, "engines": {"node": "*"}}, "node_modules/compress-commons": {"version": "0.2.9", "resolved": "https://registry.npmjs.org/compress-commons/-/compress-commons-0.2.9.tgz", "integrity": "sha1-Qi2SdDDAGr0GzUVbbfwEy0z4ADw=", "dev": true, "dependencies": {"buffer-crc32": "~0.2.1", "crc32-stream": "~0.3.1", "node-int64": "~0.3.0", "readable-stream": "~1.0.26"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/compress-commons/node_modules/isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "node_modules/compress-commons/node_modules/readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/compress-commons/node_modules/string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=", "dev": true}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true}, "node_modules/concat-stream": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==", "dev": true, "engines": ["node >= 0.8"], "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/constantinople": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/constantinople/-/constantinople-3.1.2.tgz", "integrity": "sha512-yePcBqEFhLOqSBtwYOGGS1exHo/s1xjekXiinh4itpNQGCu4KA1euPh1fg07N2wMITZXQkBz75Ntdt1ctGZouw==", "dev": true, "dependencies": {"@types/babel-types": "^7.0.0", "@types/babylon": "^6.16.2", "babel-types": "^6.26.0", "babylon": "^6.18.0"}}, "node_modules/content-disposition": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.3.tgz", "integrity": "sha512-ExO0774ikEObIAEV9kDo50o+79VCUdEB6n6lzKgGwupcVeRlhrj3qGAfwq8G6uBJjkqLrhT0qEYFcWng8z1z0g==", "dev": true, "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz", "integrity": "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.3.1.tgz", "integrity": "sha1-5+Ch+e9DtMi6klxcWpboBtFoc7s=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/cookie-parser": {"version": "1.4.4", "resolved": "https://registry.npmjs.org/cookie-parser/-/cookie-parser-1.4.4.tgz", "integrity": "sha512-lo13tqF3JEtFO7FyA49CqbhaFkskRJ0u/UAiINgrIXeRCY41c88/zxtrECl8AKH3B0hj9q10+h3Kt8I7KlW4tw==", "dev": true, "dependencies": {"cookie": "0.3.1", "cookie-signature": "1.0.6"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw=", "dev": true}, "node_modules/core-js": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/core-js/-/core-js-2.6.9.tgz", "integrity": "sha512-HOpZf6eXmnl7la+cUdMnLvUxKNqLUzJvgIziQ0DiF3JwSImNphIqdGqzj6hIKyX04MmV0poclQ7+wjWvxQyR2A==", "deprecated": "core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.", "dev": true, "hasInstallScript": true}, "node_modules/core-util-is": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "dev": true}, "node_modules/crc": {"version": "3.8.0", "resolved": "https://registry.npmjs.org/crc/-/crc-3.8.0.tgz", "integrity": "sha512-iX3mfgcTMIq3ZKLIsVFAbv7+Mc10kxabAGQb8HvjA1o3T1PIYprbakQ65d3I+2HGHt6nSKkM9PYjgoJO2KcFBQ==", "dev": true, "dependencies": {"buffer": "^5.1.0"}}, "node_modules/crc32-stream": {"version": "0.3.4", "resolved": "https://registry.npmjs.org/crc32-stream/-/crc32-stream-0.3.4.tgz", "integrity": "sha1-c7wltF+sHbZjIjGnv86JJ+nwZVI=", "dev": true, "dependencies": {"buffer-crc32": "~0.2.1", "readable-stream": "~1.0.24"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/crc32-stream/node_modules/isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "node_modules/crc32-stream/node_modules/readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/crc32-stream/node_modules/string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=", "dev": true}, "node_modules/d": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/d/-/d-1.0.1.tgz", "integrity": "sha512-m62ShEObQ39CfralilEQRjH6oAMtNCV1xJyEx5LpRYUVN+EviphDgUc/F3hnYbADmkiNs67Y+3ylmlG7Lnu+FA==", "dev": true, "dependencies": {"es5-ext": "^0.10.50", "type": "^1.0.1"}}, "node_modules/dashdash": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/decamelize": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/decimal.js": {"version": "10.2.0", "resolved": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.2.0.tgz", "integrity": "sha512-vDPw+rDgn3bZe1+F/pyEwb1oMG2XTlRVgAa6B4KccTEpYgF8w6eQllVbQcfIJnZyvzFtFpxnpGtx8dd7DJp/Rw==", "dev": true}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "engines": {"node": ">=0.4.0"}}, "node_modules/denque": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/denque/-/denque-1.4.1.tgz", "integrity": "sha512-OfzPuSZKGcgr96rf1oODnfjqBFmr1DVoc/TrItj3Ohe0Ah1C5WX5Baquw/9U9KovnQ88EqmJbD66rKYUQYN1tQ==", "dev": true, "engines": {"node": ">=0.10"}}, "node_modules/depd": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/dequeue": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/dequeue/-/dequeue-1.0.5.tgz", "integrity": "sha512-2FIVJZTaWhUj0Y2uKmDAasTP6ZwFWRjkRc01MYN5jFm96iIzkYyNzGADfJ13C5W7CTN7XO9mBYDcVB68eNybBA==", "engines": {"node": "*"}}, "node_modules/destroy": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=", "dev": true}, "node_modules/dicer": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/dicer/-/dicer-0.2.5.tgz", "integrity": "sha1-WZbAhrszIYyBLAkL3cCc0S+stw8=", "dev": true, "dependencies": {"readable-stream": "1.1.x", "streamsearch": "0.1.2"}, "engines": {"node": ">=0.8.0"}}, "node_modules/dicer/node_modules/isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "node_modules/dicer/node_modules/readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha1-fPTFTvZI44EwhMY23SB54WbAgdk=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/dicer/node_modules/string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=", "dev": true}, "node_modules/dns-equal": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/dns-equal/-/dns-equal-1.0.0.tgz", "integrity": "sha512-z+paD6YUQsk+AbGCEM4PrOXSss5gd66QfcVBFTKR/HpFL9jCqikS94HYwKww6fQyO7IxrIIyUu+g0Ka9tUS2Cg=="}, "node_modules/dns-packet": {"version": "5.4.0", "resolved": "https://registry.npmjs.org/dns-packet/-/dns-packet-5.4.0.tgz", "integrity": "sha512-EgqGeaBB8hLiHLZtp/IbaDQTL8pZ0+IvwzSHA6d7VyMDM+B9hgddEMa9xjK5oYnw0ci0JQ6g2XCD7/f6cafU6g==", "dependencies": {"@leichtgewicht/ip-codec": "^2.0.1"}, "engines": {"node": ">=6"}}, "node_modules/doctypes": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/doctypes/-/doctypes-1.1.0.tgz", "integrity": "sha1-6oCxBqh1OHdOijpKWv4pPeSJ4Kk=", "dev": true}, "node_modules/duplexify": {"version": "3.7.1", "resolved": "https://registry.npmjs.org/duplexify/-/duplexify-3.7.1.tgz", "integrity": "sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g==", "dev": true, "dependencies": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}}, "node_modules/ecc-jsbn": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "integrity": "sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==", "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "dev": true}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="}, "node_modules/encodeurl": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/end-of-stream": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.1.tgz", "integrity": "sha512-1MkrZNvWTKCaigbn+W15elq2BB/L22nqrSY5DKlo3X6+vclJm8Bb5djXJBmEX6fS3+zCh/F4VBK5Z2KxJt4s2Q==", "dev": true, "dependencies": {"once": "^1.4.0"}}, "node_modules/env-paths": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/env-paths/-/env-paths-2.2.1.tgz", "integrity": "sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==", "engines": {"node": ">=6"}}, "node_modules/es5-ext": {"version": "0.10.51", "resolved": "https://registry.npmjs.org/es5-ext/-/es5-ext-0.10.51.tgz", "integrity": "sha512-oRpWzM2WcLHVKpnrcyB7OW8j/s67Ba04JCm0WnNv3RiABSvs7mrQlutB8DBv793gKcp0XENR8Il8WxGTlZ73gQ==", "dev": true, "dependencies": {"es6-iterator": "~2.0.3", "es6-symbol": "~3.1.1", "next-tick": "^1.0.0"}}, "node_modules/es6-iterator": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/es6-iterator/-/es6-iterator-2.0.3.tgz", "integrity": "sha1-p96IkUGgWpSwhUQDstCg+/qY87c=", "dev": true, "dependencies": {"d": "1", "es5-ext": "^0.10.35", "es6-symbol": "^3.1.1"}}, "node_modules/es6-map": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/es6-map/-/es6-map-0.1.5.tgz", "integrity": "sha1-kTbgUD3MBqMBaQ8LsU/042TpSfA=", "dev": true, "dependencies": {"d": "1", "es5-ext": "~0.10.14", "es6-iterator": "~2.0.1", "es6-set": "~0.1.5", "es6-symbol": "~3.1.1", "event-emitter": "~0.3.5"}}, "node_modules/es6-set": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/es6-set/-/es6-set-0.1.5.tgz", "integrity": "sha1-0rPsXU2ADO2BjbU40ol02wpzzLE=", "dev": true, "dependencies": {"d": "1", "es5-ext": "~0.10.14", "es6-iterator": "~2.0.1", "es6-symbol": "3.1.1", "event-emitter": "~0.3.5"}}, "node_modules/es6-set/node_modules/es6-symbol": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/es6-symbol/-/es6-symbol-3.1.1.tgz", "integrity": "sha1-vwDvT9q2uhtG7Le2KbTH7VcVzHc=", "dev": true, "dependencies": {"d": "1", "es5-ext": "~0.10.14"}}, "node_modules/es6-symbol": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/es6-symbol/-/es6-symbol-3.1.2.tgz", "integrity": "sha512-/ZypxQsArlv+KHpGvng52/Iz8by3EQPxhmbuz8yFG89N/caTFBSbcXONDw0aMjy827gQg26XAjP4uXFvnfINmQ==", "dev": true, "dependencies": {"d": "^1.0.1", "es5-ext": "^0.10.51"}}, "node_modules/escalade": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz", "integrity": "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "dev": true}, "node_modules/escape-latex": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/escape-latex/-/escape-latex-1.2.0.tgz", "integrity": "sha512-nV5aVWW1K0wEiUIEdZ4erkGGH8mDxGyxSeqPzRNtWP7ataw+/olFObw7hujFWlVjNsaDFw5VZ5NzVSIqRgfTiw==", "dev": true}, "node_modules/esutils": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz", "integrity": "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "resolved": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/event-emitter": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/event-emitter/-/event-emitter-0.3.5.tgz", "integrity": "sha1-34xp7vFkeSPHFXuc6DhAYQsCzDk=", "dev": true, "dependencies": {"d": "1", "es5-ext": "~0.10.14"}}, "node_modules/events": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/events/-/events-1.1.1.tgz", "integrity": "sha1-nr23Y1rQmccNzEwqH1AEKI6L2SQ=", "dev": true, "engines": {"node": ">=0.4.x"}}, "node_modules/express": {"version": "4.17.1", "resolved": "https://registry.npmjs.org/express/-/express-4.17.1.tgz", "integrity": "sha512-mHJ9O79RqluphRrcw2X/GTh3k9tVv8YcoyY4Kkh4WDMUYKRZUq0h1o0w2rrrxBqM7VoeUVqgb27xlEMXTnYt4g==", "dev": true, "dependencies": {"accepts": "~1.3.7", "array-flatten": "1.1.1", "body-parser": "1.19.0", "content-disposition": "0.5.3", "content-type": "~1.0.4", "cookie": "0.4.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "~1.1.2", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.5", "qs": "6.7.0", "range-parser": "~1.2.1", "safe-buffer": "5.1.2", "send": "0.17.1", "serve-static": "1.14.1", "setprototypeof": "1.1.1", "statuses": "~1.5.0", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/express-zip": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/express-zip/-/express-zip-2.0.1.tgz", "integrity": "sha1-z6aKfnQdGePLuMk0b7UhL9MHwVo=", "dev": true, "dependencies": {"async": "0.9.0", "zip-stream": "0.5.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/express/node_modules/cookie": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.4.0.tgz", "integrity": "sha512-+Hp8fLp57wnUSt0tY0tHEXh4voZRDnoIrZPqlo3DPiI4y9lwg/jqx+1Om94/W6ZaPDOUbnjOt/99w66zk+l1Xg==", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/extend": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz", "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==", "dev": true}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="}, "node_modules/fd-slicer": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.1.0.tgz", "integrity": "sha512-cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g==", "dependencies": {"pend": "~1.2.0"}}, "node_modules/fill-range": {"version": "7.0.1", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz", "integrity": "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz", "integrity": "sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA==", "dev": true, "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/form-data": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz", "integrity": "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/formidable": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/formidable/-/formidable-1.2.1.tgz", "integrity": "sha512-Fs9VRguL0gqGHkXS5GQiMCr1VhZBxz0JnJs4JmMp/2jL18Fmbzvv7vOFRU+U8TBkHEE/CX1qDXzJplVULgsLeg==", "deprecated": "Please upgrade to latest, formidable@v2 or formidable@v3! Check these notes: https://bit.ly/2ZEqIau", "dev": true}, "node_modules/forwarded": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/forwarded/-/forwarded-0.1.2.tgz", "integrity": "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/fraction.js": {"version": "4.0.12", "resolved": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.0.12.tgz", "integrity": "sha512-8Z1K0VTG4hzYY7kA/1sj4/r1/RWLBD3xwReT/RCrUCbzPszjNQCCsy3ktkU/eaEqX3MYa4pY37a52eiBlPMlhA==", "dev": true, "engines": {"node": "*"}}, "node_modules/fresh": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/fs-constants": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs-constants/-/fs-constants-1.0.0.tgz", "integrity": "sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==", "dev": true}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true}, "node_modules/fsevents": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.2.tgz", "integrity": "sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==", "hasInstallScript": true, "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==", "dev": true}, "node_modules/generate-function": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/generate-function/-/generate-function-2.3.1.tgz", "integrity": "sha512-eeB5GfMNeevm/GRYq20ShmsaGcmI81kIX2K9XQx5miC8KdHaC6Jm0qQ8ZNeGOi7wYB8OsdxKs+Y2oVuTFuVwKQ==", "dev": true, "dependencies": {"is-property": "^1.0.2"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/getpass": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz", "integrity": "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/glob": {"version": "7.1.4", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.4.tgz", "integrity": "sha512-hkLPepehmnKk41pUGm3sYxoFs/umurYfYJCerbXEyFIWcAzvpipAgVkBqqT9RBKMGjnq6kMuyYwha6csxbiM1A==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/glob-parent": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-3.1.0.tgz", "integrity": "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=", "dev": true, "dependencies": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}}, "node_modules/glob-stream": {"version": "6.1.0", "resolved": "https://registry.npmjs.org/glob-stream/-/glob-stream-6.1.0.tgz", "integrity": "sha1-cEXJlBOz65SIjYOrRtC0BMx73eQ=", "dev": true, "dependencies": {"extend": "^3.0.0", "glob": "^7.1.1", "glob-parent": "^3.1.0", "is-negated-glob": "^1.0.0", "ordered-read-streams": "^1.0.0", "pumpify": "^1.3.5", "readable-stream": "^2.1.5", "remove-trailing-separator": "^1.0.1", "to-absolute-glob": "^2.0.0", "unique-stream": "^2.0.2"}, "engines": {"node": ">= 0.10"}}, "node_modules/graceful-fs": {"version": "4.2.2", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.2.tgz", "integrity": "sha512-IItsdsea19BoLC7ELy13q1iJFNmd7ofZH5+X/pJr90/nRoPEX0DJo1dHDbgtYWOhJhcCgMDTOw84RZ72q6lB+Q==", "dev": true}, "node_modules/has": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "dev": true, "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "engines": {"node": ">=8"}}, "node_modules/help-me": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/help-me/-/help-me-1.1.0.tgz", "integrity": "sha1-jy1QjQYAtKRW2i8IZVbn5cBWo8Y=", "dev": true, "dependencies": {"callback-stream": "^1.0.2", "glob-stream": "^6.1.0", "through2": "^2.0.1", "xtend": "^4.0.0"}}, "node_modules/hexy": {"version": "0.3.4", "resolved": "https://registry.npmjs.org/hexy/-/hexy-0.3.4.tgz", "integrity": "sha512-P2V2yx+XhEJRjXCe9IlRzbcL33plTaIjn+OSvgWu0Z9+2WkL71d+eC4jQ91rooM+F0JNERnENH6mpHlpcT+qlA==", "bin": {"hexy": "bin/hexy_cmd.js"}, "engines": {"node": ">=10.4"}}, "node_modules/http-errors": {"version": "1.7.2", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-1.7.2.tgz", "integrity": "sha512-uUQBt3H/cSIVfch6i1EuPNy/YsRSOUBXTVfZ+yR7Zjez3qjBz6i9+i4zjNaoqcoFVI4lQJ5plg63TvGfRSDCRg==", "dev": true, "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/https": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/https/-/https-1.0.0.tgz", "integrity": "sha1-PDfHrhqO65ZpBKKtHpdaGUt+06Q=", "dev": true}, "node_modules/humanize": {"version": "0.0.9", "resolved": "https://registry.npmjs.org/humanize/-/humanize-0.0.9.tgz", "integrity": "sha512-bvZZ7vXpr1RKoImjuQ45hJb5OvE2oJafHysiD/AL3nkqTZH2hFCjQ3YZfCd63FefDitbJze/ispUPP0gfDsT2Q==", "engines": {"node": "*"}}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "dev": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.1.13", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.13.tgz", "integrity": "sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg==", "dev": true}, "node_modules/immediate": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/immediate/-/immediate-3.0.6.tgz", "integrity": "sha1-nbHb0Pr43m++D13V5Wu2BigN5ps=", "dev": true}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "dev": true, "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/influx": {"version": "5.4.1", "resolved": "https://registry.npmjs.org/influx/-/influx-5.4.1.tgz", "integrity": "sha512-1gqUPpi/v3PUWwL/ZR7VzWeHrtKfQv/ToFWbIfPF/4aYDtZAB67GEOR7lI1vkxnoPNUdtBq5/esAWKtjCkAAnA==", "dev": true}, "node_modules/inherits": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "dev": true}, "node_modules/ipaddr.js": {"version": "1.9.0", "resolved": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.0.tgz", "integrity": "sha512-M4Sjn6N/+O6/IXSJseKqHoFc+5FdGJ22sXqnjTpdZweHK64MzEPAyQZyEU3R/KRv2GLoa7nNtg/C2Ev6m7z+eA==", "dev": true, "engines": {"node": ">= 0.10"}}, "node_modules/is-absolute": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-absolute/-/is-absolute-1.0.0.tgz", "integrity": "sha512-dOWoqflvcydARa360Gvv18DZ/gRuHKi2NU/wU5X1ZFzdYfH29nkiNZsF3mp4OJ3H4yo9Mx8A/uAGNzpzPN3yBA==", "dev": true, "dependencies": {"is-relative": "^1.0.0", "is-windows": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-binary-path": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz", "integrity": "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "dev": true}, "node_modules/is-expression": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-expression/-/is-expression-3.0.0.tgz", "integrity": "sha1-Oayqa+f9HzRx3ELHQW5hwkMXrJ8=", "dev": true, "dependencies": {"acorn": "~4.0.2", "object-assign": "^4.0.1"}}, "node_modules/is-expression/node_modules/acorn": {"version": "4.0.13", "resolved": "https://registry.npmjs.org/acorn/-/acorn-4.0.13.tgz", "integrity": "sha1-EFSVrlNh1pe9GVyCUZLhrX8lN4c=", "dev": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "dev": true, "dependencies": {"is-extglob": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-negated-glob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-negated-glob/-/is-negated-glob-1.0.0.tgz", "integrity": "sha1-aRC8pdqMleeEtXUbl2z1oQ/uNtI=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "engines": {"node": ">=0.12.0"}}, "node_modules/is-promise": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-promise/-/is-promise-2.1.0.tgz", "integrity": "sha1-eaKp7OfwlugPNtKy87wWwf9L8/o=", "dev": true}, "node_modules/is-property": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-property/-/is-property-1.0.2.tgz", "integrity": "sha1-V/4cTkhHTt1lsJkR8msc1Ald2oQ=", "dev": true}, "node_modules/is-regex": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/is-regex/-/is-regex-1.0.4.tgz", "integrity": "sha1-VRdIm1RwkbCTDglWVM7SXul+lJE=", "dev": true, "dependencies": {"has": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-relative": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-relative/-/is-relative-1.0.0.tgz", "integrity": "sha512-Kw/ReK0iqwKeu0MITLFuj0jbPAmEiOsIwyIXvvbfa6QfmN9pkD1M+8pdk7Rl/dTKbH34/XBFMbgD4iMJhLQbGA==", "dev": true, "dependencies": {"is-unc-path": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-unc-path": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-unc-path/-/is-unc-path-1.0.0.tgz", "integrity": "sha512-mrGpVd0fs7WWLfVsStvgF6iEJnbjDFZh9/emhRDcGWTduTfNHd9CHeUwH3gYIjdbwo4On6hunkztwOaAw0yllQ==", "dev": true, "dependencies": {"unc-path-regex": "^0.1.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-windows": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-1.0.2.tgz", "integrity": "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "node_modules/javascript-natural-sort": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/javascript-natural-sort/-/javascript-natural-sort-0.7.1.tgz", "integrity": "sha1-+eIwPUUH9tdDVac2ZNFED7Wg71k=", "dev": true}, "node_modules/jmespath": {"version": "0.15.0", "resolved": "https://registry.npmjs.org/jmespath/-/jmespath-0.15.0.tgz", "integrity": "sha1-o/Iiqarp+Wb10nx5ZRDigJF2Qhc=", "dev": true, "engines": {"node": ">= 0.6.0"}}, "node_modules/js-stringify": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/js-stringify/-/js-stringify-1.0.2.tgz", "integrity": "sha1-Fzb939lyTyijaCrcYjCufk6Weds=", "dev": true}, "node_modules/jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg=="}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true}, "node_modules/jsrsasign": {"version": "10.5.27", "resolved": "https://registry.npmjs.org/jsrsasign/-/jsrsasign-10.5.27.tgz", "integrity": "sha512-1F4LmDeJZHYwoVvB44jEo2uZL3XuwYNzXCDOu53Ui6vqofGQ/gCYDmaxfVZtN0TGd92UKXr/BONcfrPonUIcQQ==", "funding": {"url": "https://github.com/kjur/jsrsasign#donations"}}, "node_modules/jstransformer": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/jstransformer/-/jstransformer-1.0.0.tgz", "integrity": "sha1-7Yvwkh4vPx7U1cGkT2hwntJHIsM=", "dev": true, "dependencies": {"is-promise": "^2.0.0", "promise": "^7.0.1"}}, "node_modules/jszip": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/jszip/-/jszip-3.2.2.tgz", "integrity": "sha512-NmKajvAFQpbg3taXQXr/ccS2wcucR1AZ+NtyWp2Nq7HHVsXhcJFR8p0Baf32C2yVvBylFWVeKf+WI2AnvlPhpA==", "dev": true, "dependencies": {"lie": "~3.3.0", "pako": "~1.0.2", "readable-stream": "~2.3.6", "set-immediate-shim": "~1.0.1"}}, "node_modules/kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/lazy-cache": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/lazystream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/lazystream/-/lazystream-1.0.0.tgz", "integrity": "sha1-9plf4PggOS9hOWvolGJAe7dxaOQ=", "dev": true, "dependencies": {"readable-stream": "^2.0.5"}, "engines": {"node": ">= 0.6.3"}}, "node_modules/leven": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/leven/-/leven-2.1.0.tgz", "integrity": "sha1-wuep93IJTe6dNCAq6KzORoeHVYA=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/lie": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/lie/-/lie-3.3.0.tgz", "integrity": "sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==", "dev": true, "dependencies": {"immediate": "~3.0.5"}}, "node_modules/lodash": {"version": "4.17.15", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.15.tgz", "integrity": "sha512-8xOcRHvCjnocdS5cpwXQXVzmmh5e5+saE2QGoeQmbKmRS6J3VQppPOIt0MnmE+4xlZoumy0GPG0D0MVIQbNA1A==", "dev": true}, "node_modules/lodash.defaults": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/lodash.defaults/-/lodash.defaults-4.2.0.tgz", "integrity": "sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw=", "dev": true}, "node_modules/lodash.difference": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.difference/-/lodash.difference-4.5.0.tgz", "integrity": "sha1-nMtOUF1Ia5FlE0V3KIWi3yf9AXw=", "dev": true}, "node_modules/lodash.flatten": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/lodash.flatten/-/lodash.flatten-4.4.0.tgz", "integrity": "sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8=", "dev": true}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "resolved": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "integrity": "sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=", "dev": true}, "node_modules/lodash.union": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/lodash.union/-/lodash.union-4.6.0.tgz", "integrity": "sha1-SLtQiECfFvGCFmZkHETdGqrjzYg=", "dev": true}, "node_modules/long": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/long/-/long-4.0.0.tgz", "integrity": "sha512-XsP+KhQif4bjX1kbuSiySJFNAehNxgLb6hPRGJ9QsUr8ajHkuXGdrHmFUTUUXhDwVX2R5bY4JNZEwbUiMhV+MA=="}, "node_modules/longest": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/longest/-/longest-1.0.1.tgz", "integrity": "sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "dev": true, "dependencies": {"yallist": "^3.0.2"}}, "node_modules/ltx": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/ltx/-/ltx-3.0.0.tgz", "integrity": "sha512-bu3/4/ApUmMqVNuIkHaRhqVtEi6didYcBDIF56xhPRCzVpdztCipZ62CUuaxMlMBUzaVL93+4LZRqe02fuAG6A==", "engines": {"node": ">= 12.4.0"}}, "node_modules/mathjs": {"version": "6.2.2", "resolved": "https://registry.npmjs.org/mathjs/-/mathjs-6.2.2.tgz", "integrity": "sha512-l0qw0meh1rcGaFMh248wx6NE5RVWA2bleq+5xF44XAaPMHcou+cKq/0ASBnTDsp0zNAbUYm7tGkDEe6QHbzU8g==", "dev": true, "dependencies": {"complex.js": "2.0.11", "decimal.js": "10.2.0", "escape-latex": "1.2.0", "fraction.js": "4.0.12", "javascript-natural-sort": "0.7.1", "seed-random": "2.2.0", "tiny-emitter": "2.1.0", "typed-function": "1.1.1"}, "bin": {"mathjs": "bin/cli.js"}, "engines": {"node": ">= 8"}}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/merge-descriptors": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=", "dev": true}, "node_modules/methods": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "integrity": "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==", "dev": true, "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.40.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.40.0.tgz", "integrity": "sha512-jYdeOMPy9vnxEqFRRo6ZvTZ8d9oPb+k18PKoYNYUe2stVEBPPwsln/qWzdbmaIvnhZ9v2P+CuecK+fpUfsV2mA==", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.24", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.24.tgz", "integrity": "sha512-Wa<PERSON><PERSON>3MCl5fapm3oLxU4eYDw77IQM2ACcxQ9RIxfaC3ooc6PFuBMGZZsYpvoXS5D5QTWPieo1jjLdAm3TBP3cQ==", "dependencies": {"mime-db": "1.40.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "dev": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz", "integrity": "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=", "dev": true}, "node_modules/mkdirp": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "dev": true, "dependencies": {"minimist": "0.0.8"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/mkdirp/node_modules/minimist": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=", "dev": true}, "node_modules/modbusjs": {"version": "0.0.11", "resolved": "https://registry.npmjs.org/modbusjs/-/modbusjs-0.0.11.tgz", "integrity": "sha512-ZHfKfO/GjOBVZX/7G6mSbQIKqY4yRIvL5WhB/E/mhQNMnPux/2T8GAJexRtJugbIhs5e6+JjyELPyoRazCykAQ==", "dev": true}, "node_modules/moment": {"version": "2.24.0", "resolved": "https://registry.npmjs.org/moment/-/moment-2.24.0.tgz", "integrity": "sha512-bV7f+6l2QigeBBZSM/6yTNq4P2fNpSWj/0e7jQcy87A8e7o2nAfP/34/2ky5Vw4B9S446EtIhodAzkFCcR4dQg==", "dev": true, "engines": {"node": "*"}}, "node_modules/morgan": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/morgan/-/morgan-1.9.1.tgz", "integrity": "sha512-HQStPIV4y3afTiCYVxirakhlCfGkI161c76kKFca7Fk1JusM//Qeo1ej2XaMniiNeaZklMVrh3vTtIzpzwbpmA==", "dev": true, "dependencies": {"basic-auth": "~2.0.0", "debug": "2.6.9", "depd": "~1.1.2", "on-finished": "~2.3.0", "on-headers": "~1.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/mqtt": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/mqtt/-/mqtt-3.0.0.tgz", "integrity": "sha512-0nKV6MAc1ibKZwaZQUTb3iIdT4NVpj541BsYrqrGBcQdQ7Jd0MnZD1/6/nj1UFdGTboK9ZEUXvkCu2nPCugHFA==", "dev": true, "dependencies": {"base64-js": "^1.3.0", "commist": "^1.0.0", "concat-stream": "^1.6.2", "end-of-stream": "^1.4.1", "es6-map": "^0.1.5", "help-me": "^1.0.1", "inherits": "^2.0.3", "minimist": "^1.2.0", "mqtt-packet": "^6.0.0", "pump": "^3.0.0", "readable-stream": "^2.3.6", "reinterval": "^1.1.0", "split2": "^3.1.0", "websocket-stream": "^5.1.2", "xtend": "^4.0.1"}, "bin": {"mqtt": "mqtt.js", "mqtt_pub": "bin/pub.js", "mqtt_sub": "bin/sub.js"}, "engines": {"node": ">=4.0.0"}}, "node_modules/mqtt-packet": {"version": "6.2.1", "resolved": "https://registry.npmjs.org/mqtt-packet/-/mqtt-packet-6.2.1.tgz", "integrity": "sha512-ZxG5QVb7+gMix5n4DClym9dQoCZC6DoNEqgMkMi/GMXvIU4Wsdx+/6KBavw50HHFH9kN1lBSY7phxNlAS2+jnw==", "dev": true, "dependencies": {"bl": "^1.2.2", "inherits": "^2.0.3", "process-nextick-args": "^2.0.0", "safe-buffer": "^5.1.2"}}, "node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "node_modules/multer": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/multer/-/multer-1.4.2.tgz", "integrity": "sha512-xY8pX7V+ybyUpbYMxtjM9KAiD9ixtg5/JkeKUTD6xilfDv0vzzOFcCp4Ljb1UU3tSOM3VTZtKo63OmzOrGi3Cg==", "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10.", "dev": true, "dependencies": {"append-field": "^1.0.0", "busboy": "^0.2.11", "concat-stream": "^1.5.2", "mkdirp": "^0.5.1", "object-assign": "^4.1.1", "on-finished": "^2.3.0", "type-is": "^1.6.4", "xtend": "^4.0.0"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/multicast-dns": {"version": "7.2.5", "resolved": "https://registry.npmjs.org/multicast-dns/-/multicast-dns-7.2.5.tgz", "integrity": "sha512-2eznPJP8z2BFLX50tf0LuODrpINqP1RVIm/CObbTcBRITQgmC/TjcREF1NeTBzIcR5XO/ukWo+YHOjBbFwIupg==", "dependencies": {"dns-packet": "^5.2.2", "thunky": "^1.0.2"}, "bin": {"multicast-dns": "cli.js"}}, "node_modules/mysql": {"version": "2.17.1", "resolved": "https://registry.npmjs.org/mysql/-/mysql-2.17.1.tgz", "integrity": "sha512-7vMqHQ673SAk5C8fOzTG2LpPcf3bNt0oL3sFpxPEEFp1mdlDcrLK0On7z8ZYKaaHrHwNcQ/MTUz7/oobZ2OyyA==", "dev": true, "dependencies": {"bignumber.js": "7.2.1", "readable-stream": "2.3.6", "safe-buffer": "5.1.2", "sqlstring": "2.3.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/mysql2": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/mysql2/-/mysql2-1.7.0.tgz", "integrity": "sha512-xTWWQPjP5rcrceZQ7CSTKR/4XIDeH/cRkNH/uzvVGQ7W5c7EJ0dXeJUusk7OKhIoHj7uFKUxDVSCfLIl+jluog==", "dev": true, "dependencies": {"denque": "^1.4.1", "generate-function": "^2.3.1", "iconv-lite": "^0.5.0", "long": "^4.0.0", "lru-cache": "^5.1.1", "named-placeholders": "^1.1.2", "seq-queue": "^0.0.5", "sqlstring": "^2.3.1"}, "engines": {"node": ">= 8.0"}}, "node_modules/mysql2/node_modules/iconv-lite": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.5.0.tgz", "integrity": "sha512-NnEhI9hIEKHOzJ4f697DMz9IQEXr/MMJ5w64vN2/4Ai+wRnvV7SBrL0KLoRlwaKVghOc7LQ5YkPLuX146b6Ydw==", "dev": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/named-placeholders": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-1.1.2.tgz", "integrity": "sha512-wiFWqxoLL3PGVReSZpjLVxyJ1bRqe+KKJVbr4hGs1KWfTZTQyezHFBbuKj9hsizHyGV2ne7EMjHdxEGAybD5SA==", "dev": true, "dependencies": {"lru-cache": "^4.1.3"}, "engines": {"node": ">=6.0.0"}}, "node_modules/named-placeholders/node_modules/lru-cache": {"version": "4.1.5", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.5.tgz", "integrity": "sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==", "dev": true, "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/named-placeholders/node_modules/yallist": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz", "integrity": "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=", "dev": true}, "node_modules/negotiator": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.2.tgz", "integrity": "sha512-hZXc7K2e+PgeI1eDBe/10Ard4ekbfrrqG8Ep+8Jmf4JID2bNg7NvCPOZN+kfF574pFQI7mum2AUqDidoKqcTOw==", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/next-tick": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/next-tick/-/next-tick-1.0.0.tgz", "integrity": "sha1-yobR/ogoFpsBICCOPchCS524NCw=", "dev": true}, "node_modules/node-int64": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/node-int64/-/node-int64-0.3.3.tgz", "integrity": "sha1-LW5rLs5d6FiLQ9iNG8QbJs0fqE0=", "dev": true}, "node_modules/node-opcua": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua/-/node-opcua-2.81.0.tgz", "integrity": "sha512-r590PNEVJ1esJYJ8CLp3b6tU/5GI0NOzLxFWdW+C19ms5YQYXq2TulgFE+G+o3Q34zi5yiwRUKRFam4mhX86og==", "dependencies": {"@types/semver": "^7.3.12", "chalk": "4.1.2", "node-opcua-address-space": "2.81.0", "node-opcua-address-space-for-conformance-testing": "2.81.0", "node-opcua-aggregates": "2.81.0", "node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-certificate-manager": "2.77.0", "node-opcua-client": "2.81.0", "node-opcua-client-crawler": "2.81.0", "node-opcua-client-proxy": "2.81.0", "node-opcua-common": "2.81.0", "node-opcua-constants": "2.77.0", "node-opcua-crypto": "^1.11.0", "node-opcua-data-access": "2.81.0", "node-opcua-data-model": "2.81.0", "node-opcua-data-value": "2.81.0", "node-opcua-debug": "2.77.0", "node-opcua-enum": "2.77.0", "node-opcua-factory": "2.81.0", "node-opcua-hostname": "2.77.0", "node-opcua-nodeid": "2.81.0", "node-opcua-nodesets": "2.77.0", "node-opcua-numeric-range": "2.81.0", "node-opcua-packet-analyzer": "2.81.0", "node-opcua-secure-channel": "2.81.0", "node-opcua-server": "2.81.0", "node-opcua-server-discovery": "2.81.0", "node-opcua-service-browse": "2.81.0", "node-opcua-service-call": "2.81.0", "node-opcua-service-discovery": "2.81.0", "node-opcua-service-endpoints": "2.81.0", "node-opcua-service-filter": "2.81.0", "node-opcua-service-history": "2.81.0", "node-opcua-service-node-management": "2.81.0", "node-opcua-service-query": "2.81.0", "node-opcua-service-read": "2.81.0", "node-opcua-service-register-node": "2.81.0", "node-opcua-service-secure-channel": "2.81.0", "node-opcua-service-session": "2.81.0", "node-opcua-service-subscription": "2.81.0", "node-opcua-service-translate-browse-path": "2.81.0", "node-opcua-service-write": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-transport": "2.81.0", "node-opcua-types": "2.81.0", "node-opcua-utils": "2.77.0", "node-opcua-variant": "2.81.0", "node-opcua-vendor-diagnostic": "2.81.0", "semver": "^7.3.7"}, "engines": {"node": ">=8.10"}, "funding": {"url": "https://github.com/sponsors/erossignon"}}, "node_modules/node-opcua-address-space": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-address-space/-/node-opcua-address-space-2.81.0.tgz", "integrity": "sha512-GMmUlPR0HC+4qeha9U8IMiQX1286HyGfnhQhTaHQLbCbndfJOcWpqMc+AWBOJL39v80nxppx1W3HTd9UvNQk9Q==", "dependencies": {"@types/lodash": "4.14.185", "@types/semver": "^7.3.12", "async": "^3.2.4", "chalk": "4.1.2", "dequeue": "^1.0.5", "lodash": "4.17.21", "node-opcua-address-space-base": "2.81.0", "node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-client-dynamic-extension-object": "2.81.0", "node-opcua-constants": "2.77.0", "node-opcua-crypto": "^1.11.0", "node-opcua-data-access": "2.81.0", "node-opcua-data-model": "2.81.0", "node-opcua-data-value": "2.81.0", "node-opcua-date-time": "2.77.0", "node-opcua-debug": "2.77.0", "node-opcua-enum": "2.77.0", "node-opcua-extension-object": "2.81.0", "node-opcua-factory": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-nodeset-ua": "2.81.0", "node-opcua-numeric-range": "2.81.0", "node-opcua-object-registry": "2.77.0", "node-opcua-pseudo-session": "2.81.0", "node-opcua-service-browse": "2.81.0", "node-opcua-service-call": "2.81.0", "node-opcua-service-history": "2.81.0", "node-opcua-service-translate-browse-path": "2.81.0", "node-opcua-service-write": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-types": "2.81.0", "node-opcua-utils": "2.77.0", "node-opcua-variant": "2.81.0", "node-opcua-xml2json": "2.77.0", "semver": "^7.3.7", "set-prototype-of": "^1.0.0", "thenify": "^3.3.1", "xml-writer": "^1.7.0"}, "engines": {"node": ">=6.10"}}, "node_modules/node-opcua-address-space-base": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-address-space-base/-/node-opcua-address-space-base-2.81.0.tgz", "integrity": "sha512-oFv5q+EYOhVraoxp8QAjfkFQQrBp1Tf/lDkm3G00qgWrXxgMmfigjjs6fb6s3uBEPdflP5YJBIB/3kseB/5CGg==", "dependencies": {"@types/lodash": "4.14.185", "node-opcua-basic-types": "2.81.0", "node-opcua-constants": "2.77.0", "node-opcua-crypto": "^1.11.0", "node-opcua-data-model": "2.81.0", "node-opcua-data-value": "2.81.0", "node-opcua-date-time": "2.77.0", "node-opcua-extension-object": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-numeric-range": "2.81.0", "node-opcua-schemas": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-types": "2.81.0", "node-opcua-variant": "2.81.0"}, "engines": {"node": ">=6.10"}}, "node_modules/node-opcua-address-space-for-conformance-testing": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-address-space-for-conformance-testing/-/node-opcua-address-space-for-conformance-testing-2.81.0.tgz", "integrity": "sha512-YR+IfE+CSiSbO7uBpwZXR4Jt9d07zd5JqNU96lrICxtdEDLpXw63+ga2t7yiW8RTaQRk6ciXY3161PfbnJKJdQ==", "dependencies": {"node-opcua-address-space": "2.81.0", "node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-data-access": "2.81.0", "node-opcua-data-model": "2.81.0", "node-opcua-data-value": "2.81.0", "node-opcua-factory": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-variant": "2.81.0"}}, "node_modules/node-opcua-address-space/node_modules/async": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/async/-/async-3.2.4.tgz", "integrity": "sha512-iAB+JbDEGXhyIUavoDl9WP/Jj106Kz9DEn1DPgYw5ruDn0e3Wgi3sKFm55sASdGBNOQB8F59d9qQ7deqrHA8wQ=="}, "node_modules/node-opcua-address-space/node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="}, "node_modules/node-opcua-aggregates": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-aggregates/-/node-opcua-aggregates-2.81.0.tgz", "integrity": "sha512-3fteTmjMbTzXWKi1nBMTMamb2tJOm63cuy2WCahCxILeyM7YNrBPSvA6kGJwffcYmZvJ/Lp+/GrRqVN8UoNR1Q==", "dependencies": {"node-opcua-address-space": "2.81.0", "node-opcua-assert": "2.77.0", "node-opcua-constants": "2.77.0", "node-opcua-data-model": "2.81.0", "node-opcua-data-value": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-numeric-range": "2.81.0", "node-opcua-server": "2.81.0", "node-opcua-service-history": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-types": "2.81.0", "node-opcua-utils": "2.77.0", "node-opcua-variant": "2.81.0"}}, "node_modules/node-opcua-assert": {"version": "2.77.0", "resolved": "https://registry.npmjs.org/node-opcua-assert/-/node-opcua-assert-2.77.0.tgz", "integrity": "sha512-LKAnJkixMt6NYtl9tc7z4R0KoqDpQpUv5uX65fTbP2BXm/Y8XvoYgbOqqLEygzYQ4fpZpCbkO9IZfmgsHz9npQ==", "dependencies": {"chalk": "4.1.2"}}, "node_modules/node-opcua-basic-types": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-basic-types/-/node-opcua-basic-types-2.81.0.tgz", "integrity": "sha512-t9YcNU42UIqEMjd25ptKYD+k6K1mXwG979BVGxd7mHTtqK13GnMjmEz4e01F4MyRvi60JXzPbeDDVzo4e7JyNg==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-buffer-utils": "2.77.0", "node-opcua-date-time": "2.77.0", "node-opcua-guid": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-status-code": "2.77.0"}}, "node_modules/node-opcua-binary-stream": {"version": "2.77.0", "resolved": "https://registry.npmjs.org/node-opcua-binary-stream/-/node-opcua-binary-stream-2.77.0.tgz", "integrity": "sha512-6OgEiMUZ29MIPBP+NHxWaZPzyHQVHW+IN1lG1hf3rjKls9Lvj4M+W0CS4aFzXH/yB6JyqCwpLFIe9ese+b4WUg==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-buffer-utils": "2.77.0"}}, "node_modules/node-opcua-buffer-utils": {"version": "2.77.0", "resolved": "https://registry.npmjs.org/node-opcua-buffer-utils/-/node-opcua-buffer-utils-2.77.0.tgz", "integrity": "sha512-CAAVvwaQtOhJhlcgcQRuuj28GEk9lb2uRrm5RwEu1+KsMogo4FcY+r8N2S7TVyXmbyAi/KtLyQjhqTyYI1uomA=="}, "node_modules/node-opcua-certificate-manager": {"version": "2.77.0", "resolved": "https://registry.npmjs.org/node-opcua-certificate-manager/-/node-opcua-certificate-manager-2.77.0.tgz", "integrity": "sha512-v47Loimcua7EdCGYlpiPeUtPmSqJanQzBDEKI1MLugZjaf+T4V4oLxDBddrep8/t1F6R0Q17VRm/otPIKnBgsQ==", "dependencies": {"@types/mkdirp": "1.0.2", "env-paths": "2.2.1", "mkdirp": "1.0.4", "node-opcua-assert": "2.77.0", "node-opcua-crypto": "^1.11.0", "node-opcua-debug": "2.77.0", "node-opcua-object-registry": "2.77.0", "node-opcua-pki": "^2.17.0", "node-opcua-status-code": "2.77.0", "thenify": "^3.3.1"}}, "node_modules/node-opcua-certificate-manager/node_modules/mkdirp": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/node-opcua-chunkmanager": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-chunkmanager/-/node-opcua-chunkmanager-2.81.0.tgz", "integrity": "sha512-yGzdDanT6dIQ2wDxwZQ/3ESTmMHJ5FnhWImZRXjtrDR2+yCW8/i7sXIa/nmuOD4HkNDUd/UCxX5i+vpjjqfIPQ==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-buffer-utils": "2.77.0", "node-opcua-factory": "2.81.0", "node-opcua-packet-assembler": "2.77.0"}}, "node_modules/node-opcua-client": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-client/-/node-opcua-client-2.81.0.tgz", "integrity": "sha512-sza3oa71uROOQYJMWOW46b2uTWeMiCActJz2B1pZ3BFMcGvb2I1FM+NGsYW54F/GiHhi2Dv8R9MDC8MtY+1ABw==", "dependencies": {"@ster5/global-mutex": "^1.2.0", "@types/async": "^3.2.15", "@types/once": "^1.4.0", "async": "^3.2.4", "callbackify": "^1.1.0", "chalk": "4.1.2", "node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-buffer-utils": "2.77.0", "node-opcua-certificate-manager": "2.77.0", "node-opcua-client-dynamic-extension-object": "2.81.0", "node-opcua-common": "2.81.0", "node-opcua-constants": "2.77.0", "node-opcua-crypto": "^1.11.0", "node-opcua-data-model": "2.81.0", "node-opcua-data-value": "2.81.0", "node-opcua-date-time": "2.77.0", "node-opcua-debug": "2.77.0", "node-opcua-extension-object": "2.81.0", "node-opcua-hostname": "2.77.0", "node-opcua-nodeid": "2.81.0", "node-opcua-object-registry": "2.77.0", "node-opcua-pki": "^2.17.0", "node-opcua-pseudo-session": "2.81.0", "node-opcua-schemas": "2.81.0", "node-opcua-secure-channel": "2.81.0", "node-opcua-service-browse": "2.81.0", "node-opcua-service-call": "2.81.0", "node-opcua-service-discovery": "2.81.0", "node-opcua-service-endpoints": "2.81.0", "node-opcua-service-filter": "2.81.0", "node-opcua-service-history": "2.81.0", "node-opcua-service-query": "2.81.0", "node-opcua-service-read": "2.81.0", "node-opcua-service-register-node": "2.81.0", "node-opcua-service-secure-channel": "2.81.0", "node-opcua-service-session": "2.81.0", "node-opcua-service-subscription": "2.81.0", "node-opcua-service-translate-browse-path": "2.81.0", "node-opcua-service-write": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-types": "2.81.0", "node-opcua-utils": "2.77.0", "node-opcua-variant": "2.81.0", "once": "^1.4.0", "thenify": "^3.3.1"}}, "node_modules/node-opcua-client-crawler": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-client-crawler/-/node-opcua-client-crawler-2.81.0.tgz", "integrity": "sha512-0ODrIH9sp94qBgUxv9n18sRzTwIAypr1RqMsQ2JjGHnzPJxAoF5cgz0vWDBgHyErQjcN5FC80Ct5tOC20pXUhQ==", "dependencies": {"@types/underscore": "^1.11.4", "async": "^3.2.4", "chalk": "4.1.2", "node-opcua-address-space": "2.81.0", "node-opcua-assert": "2.77.0", "node-opcua-client": "2.81.0", "node-opcua-constants": "2.77.0", "node-opcua-data-model": "2.81.0", "node-opcua-data-value": "2.81.0", "node-opcua-debug": "2.77.0", "node-opcua-nodeid": "2.81.0", "node-opcua-pki": "^2.17.0", "node-opcua-service-browse": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-types": "2.81.0", "node-opcua-utils": "2.77.0", "thenify": "^3.3.1", "underscore": "^1.13.6"}}, "node_modules/node-opcua-client-crawler/node_modules/async": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/async/-/async-3.2.4.tgz", "integrity": "sha512-iAB+JbDEGXhyIUavoDl9WP/Jj106Kz9DEn1DPgYw5ruDn0e3Wgi3sKFm55sASdGBNOQB8F59d9qQ7deqrHA8wQ=="}, "node_modules/node-opcua-client-dynamic-extension-object": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-client-dynamic-extension-object/-/node-opcua-client-dynamic-extension-object-2.81.0.tgz", "integrity": "sha512-PiIhy9CydLkX4CWQKH4/W4bd0EojQC37IagpTLG1J/A9+FWHd8Q0s4u7b+641+en7eKY2tsSt/IvVb1AYWtTLA==", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.77.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-data-model": "2.81.0", "node-opcua-data-value": "2.81.0", "node-opcua-debug": "2.77.0", "node-opcua-extension-object": "2.81.0", "node-opcua-factory": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-pseudo-session": "2.81.0", "node-opcua-schemas": "2.81.0", "node-opcua-service-browse": "2.81.0", "node-opcua-service-translate-browse-path": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-types": "2.81.0", "node-opcua-variant": "2.81.0"}}, "node_modules/node-opcua-client-proxy": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-client-proxy/-/node-opcua-client-proxy-2.81.0.tgz", "integrity": "sha512-IzrWNU2jozAxGljWuFi6fqvrVUj13WuRDFfYSjckZt0eqx9+Y9f197GB1b1VgkIxOTcXqhObyHf5RC65TO6zkw==", "dependencies": {"async": "^3.2.4", "node-opcua-assert": "2.77.0", "node-opcua-constants": "2.77.0", "node-opcua-data-model": "2.81.0", "node-opcua-data-value": "2.81.0", "node-opcua-debug": "2.77.0", "node-opcua-nodeid": "2.81.0", "node-opcua-pseudo-session": "2.81.0", "node-opcua-service-browse": "2.81.0", "node-opcua-service-call": "2.81.0", "node-opcua-service-read": "2.81.0", "node-opcua-service-subscription": "2.81.0", "node-opcua-service-write": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-utils": "2.77.0", "node-opcua-variant": "2.81.0", "thenify": "^3.3.1"}}, "node_modules/node-opcua-client-proxy/node_modules/async": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/async/-/async-3.2.4.tgz", "integrity": "sha512-iAB+JbDEGXhyIUavoDl9WP/Jj106Kz9DEn1DPgYw5ruDn0e3Wgi3sKFm55sASdGBNOQB8F59d9qQ7deqrHA8wQ=="}, "node_modules/node-opcua-client/node_modules/async": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/async/-/async-3.2.4.tgz", "integrity": "sha512-iAB+JbDEGXhyIUavoDl9WP/Jj106Kz9DEn1DPgYw5ruDn0e3Wgi3sKFm55sASdGBNOQB8F59d9qQ7deqrHA8wQ=="}, "node_modules/node-opcua-common": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-common/-/node-opcua-common-2.81.0.tgz", "integrity": "sha512-6ZWU8hk8yRtQc83MjGUSMnyM2FB8GtSHQg9JB8+rZhWYH6sv+FY96L2vJA570AZNddxbyuw3rbvX8hBS3akmOQ==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-crypto": "^1.11.0", "node-opcua-types": "2.81.0"}}, "node_modules/node-opcua-constants": {"version": "2.77.0", "resolved": "https://registry.npmjs.org/node-opcua-constants/-/node-opcua-constants-2.77.0.tgz", "integrity": "sha512-DZWytWhYi8xqst9y/o7pc+Ba8wvc0gTk9AjZg0fmCe9ANkWZGmRCHjLvw95mE4tk9IY/61k08hj2Pq3X1Ljhcg=="}, "node_modules/node-opcua-crypto": {"version": "1.11.0", "resolved": "https://registry.npmjs.org/node-opcua-crypto/-/node-opcua-crypto-1.11.0.tgz", "integrity": "sha512-vaJGnNeOVZvpvpdDN5qrtY4AMKcYcfrhWVzOEKqzJixTVa+Gly9MPBaLq4+Rr4xArz5pel769pp8RKiufW81Hw==", "dependencies": {"better-assert": "^1.0.2", "chalk": "^4.1.2", "hexy": "0.3.4", "jsrsasign": "^10.5.25", "sshpk": "^1.17.0"}}, "node_modules/node-opcua-data-access": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-data-access/-/node-opcua-data-access-2.81.0.tgz", "integrity": "sha512-TBS0p3Q8/yXMmLBt3R//Mj7fk5rzL16W7xDDK5aU0dkdVx4k0vBCLynLrqyZ3yhFL+RRV5d5KqB0V9t0+3h87w==", "dependencies": {"node-opcua-data-model": "2.81.0", "node-opcua-types": "2.81.0"}}, "node_modules/node-opcua-data-model": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-data-model/-/node-opcua-data-model-2.81.0.tgz", "integrity": "sha512-dgtKl3Qd12AiZBVyDNpkZsEChknimKBG7er0cG5Kf5jPT+nMMwIe9Ik9mvAOhMUFclC6A+Oe6mrbsEMXY8Qf0w==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-enum": "2.77.0", "node-opcua-factory": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-status-code": "2.77.0"}}, "node_modules/node-opcua-data-value": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-data-value/-/node-opcua-data-value-2.81.0.tgz", "integrity": "sha512-4B/YWfrU3i8CFcU8r/RtvjIqmv42PrjhnV9Hk2K7SvnY/zi+U6LbCIhwDd0RwO1UPdNtHF7xKk1q/ezQJ9ddBA==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-data-model": "2.81.0", "node-opcua-date-time": "2.77.0", "node-opcua-enum": "2.77.0", "node-opcua-factory": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-variant": "2.81.0"}}, "node_modules/node-opcua-date-time": {"version": "2.77.0", "resolved": "https://registry.npmjs.org/node-opcua-date-time/-/node-opcua-date-time-2.77.0.tgz", "integrity": "sha512-SXUeHARgMV6Tvny2JQt6riF/VGDxnJHE3z0HdpXCykZnlsfMy+78fc6PrvWOzmRUjFtGH91UYZS/X9kQR7DKQg==", "dependencies": {"long": "^4.0.0", "node-opcua-assert": "2.77.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-utils": "2.77.0"}}, "node_modules/node-opcua-debug": {"version": "2.77.0", "resolved": "https://registry.npmjs.org/node-opcua-debug/-/node-opcua-debug-2.77.0.tgz", "integrity": "sha512-ufExS03IkJMHgZ3ACqn+oh/YV2srS0cHFYnP3f3F0RgXOG+zP/h88eAXFEpBngbqhEkg6HxqcKCa9zgIy8TZRg==", "dependencies": {"chalk": "4.1.2", "hexy": "0.3.4", "node-opcua-assert": "2.77.0", "node-opcua-buffer-utils": "2.77.0"}}, "node_modules/node-opcua-enum": {"version": "2.77.0", "resolved": "https://registry.npmjs.org/node-opcua-enum/-/node-opcua-enum-2.77.0.tgz", "integrity": "sha512-AOt4d8WvuYwNl3K/fwXOXKllITEIVI2UjYq91z7sGHHBbyIIR74fOhC1qC8WROY0l1h0pYPlwbDzETngsI9Kug=="}, "node_modules/node-opcua-extension-object": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-extension-object/-/node-opcua-extension-object-2.81.0.tgz", "integrity": "sha512-S04nBHrQC7HzOYThCj6ie86zIFssVQtZuXw3NPNXTQO/RHuYW7zicBvaBR9xLJPwJvuQgWAa/89PzSfwX4FS2Q==", "dependencies": {"chalk": "4.1.2", "node-opcua-basic-types": "2.81.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-debug": "2.77.0", "node-opcua-factory": "2.81.0", "node-opcua-nodeid": "2.81.0"}}, "node_modules/node-opcua-factory": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-factory/-/node-opcua-factory-2.81.0.tgz", "integrity": "sha512-hrRcecUe2Ita8xV8VQZHhLDHIJPJmMlDgAROo6PUf941vBC8vRiP/W+W7dMX6Jxv+yAKGCzQM8AhZQcqII303g==", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-constants": "2.77.0", "node-opcua-debug": "2.77.0", "node-opcua-enum": "2.77.0", "node-opcua-guid": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-utils": "2.77.0"}}, "node_modules/node-opcua-generator": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-generator/-/node-opcua-generator-2.81.0.tgz", "integrity": "sha512-0QJQbOkKia0/s/FQIfm3Mhrz7EPreYINcBwiGGzvbSA0PWCKmhZg8cicfFi3UeCL4S9P6rj2Y8JZt7bjfpg2Vw==", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.77.0", "node-opcua-constants": "2.77.0", "node-opcua-debug": "2.77.0", "node-opcua-factory": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-schemas": "2.81.0", "node-opcua-utils": "2.77.0"}}, "node_modules/node-opcua-guid": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-guid/-/node-opcua-guid-2.81.0.tgz", "integrity": "sha512-htoAaZmCIkg75SsTnYgwdmU3HIwSEY0rK0Erm+kCIGqO6sLr6H2GhMmiqalcB+rd4Dt23Ob+CAo+xHSuL99Hwg=="}, "node_modules/node-opcua-hostname": {"version": "2.77.0", "resolved": "https://registry.npmjs.org/node-opcua-hostname/-/node-opcua-hostname-2.77.0.tgz", "integrity": "sha512-c<PERSON>woJcfchI7cJy6aF4mazrQfASJIbPtGPpyahUrI4hD07oIrI4SnmF9m3Wyy2c9BUz7uOjpkaTDY6qC19c3vlA=="}, "node_modules/node-opcua-nodeid": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-nodeid/-/node-opcua-nodeid-2.81.0.tgz", "integrity": "sha512-zYdIWRzbMmKJC7afWuq/93f9rfXJCUPxrIi2oYC7lJ7zqyUvsLp0QRMgccW+mOiYgHaPg2aNHWKxGb6CR4VH/Q==", "dependencies": {"@types/lodash": "4.14.185", "chalk": "4.1.2", "lodash": "4.17.21", "node-opcua-assert": "2.77.0", "node-opcua-constants": "2.77.0", "node-opcua-guid": "2.81.0"}}, "node_modules/node-opcua-nodeid/node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="}, "node_modules/node-opcua-nodeset-ua": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-nodeset-ua/-/node-opcua-nodeset-ua-2.81.0.tgz", "integrity": "sha512-QOPyNfUZZfHYNZo9/3xRgnb3rblMlhR4mXxIqERtkHL4lBX05MSNkuIkSRBY9JhDiRYbrHrahWL2V3XRgxUQ9Q==", "dependencies": {"node-opcua-address-space-base": "2.81.0", "node-opcua-basic-types": "2.81.0", "node-opcua-data-access": "2.81.0", "node-opcua-data-model": "2.81.0", "node-opcua-data-value": "2.81.0", "node-opcua-extension-object": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-variant": "2.81.0"}}, "node_modules/node-opcua-nodesets": {"version": "2.77.0", "resolved": "https://registry.npmjs.org/node-opcua-nodesets/-/node-opcua-nodesets-2.77.0.tgz", "integrity": "sha512-c/dZmU49VcjeD4n7Rjvbtc6G+zmwNNlQxYK7oEIlz37Awppj18bg8wb4HV/sdrJxIlSz5rBYabI3Y3owKD/BDw=="}, "node_modules/node-opcua-numeric-range": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-numeric-range/-/node-opcua-numeric-range-2.81.0.tgz", "integrity": "sha512-oE7grDmC5FMNz/CeWl1Z5xpYWItYAW4XL6Hk7Ku7d5I+g9NDjCBfvnVb6Az1YT5YEMAT/hj5IoztZZ/JLK4X5g==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-factory": "2.81.0", "node-opcua-status-code": "2.77.0"}}, "node_modules/node-opcua-object-registry": {"version": "2.77.0", "resolved": "https://registry.npmjs.org/node-opcua-object-registry/-/node-opcua-object-registry-2.77.0.tgz", "integrity": "sha512-cNCOijWWKleEyMBL9SfARraeitK6kf4l0JRLcC2aaGtZHNJDVs7l5TR0OGkO6pfRi2P1ercs+6CT+iaubBUnqw==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-debug": "2.77.0"}}, "node_modules/node-opcua-packet-analyzer": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-packet-analyzer/-/node-opcua-packet-analyzer-2.81.0.tgz", "integrity": "sha512-AMhGDkfSr5XzZOp1gDp9hz0Fv7B4Mck8cGqJTuf2Ay6TzsnPElSauQpNp4za4rt7fZ8JSNI3ucfHgZUXiExEIw==", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-debug": "2.77.0", "node-opcua-factory": "2.81.0", "node-opcua-utils": "2.77.0"}}, "node_modules/node-opcua-packet-assembler": {"version": "2.77.0", "resolved": "https://registry.npmjs.org/node-opcua-packet-assembler/-/node-opcua-packet-assembler-2.77.0.tgz", "integrity": "sha512-SFflyoiIVwZz30xT+z2Buz1km5lcZpx0Hlu/ZmYs2W/usVav2VPDYsAbvfCTkl40BjPYiVI+zTma+W0ixoeE+g==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-debug": "2.77.0"}}, "node_modules/node-opcua-pki": {"version": "2.17.0", "resolved": "https://registry.npmjs.org/node-opcua-pki/-/node-opcua-pki-2.17.0.tgz", "integrity": "sha512-CXyy2CuIV7L8VGZ+LL6HeQ/jOph/AjJ/h6/qBvs3Wsowt5Ay7VN1stO0/SDjJX/o7LysIpLuXaxrs97g+Fq/ew==", "dependencies": {"@ster5/global-mutex": "^1.2.0", "async": "^3.2.4", "byline": "^5.0.0", "chalk": "4.1.2", "chokidar": "^3.5.3", "cli-table": "^0.3.11", "minimist": "^1.2.6", "node-opcua-crypto": "^1.11.0", "progress": "^2.0.3", "thenify": "^3.3.1", "wget-improved-2": "^3.3.0", "yargs": "17.5.1", "yauzl": "^2.10.0"}, "bin": {"pki": "bin/crypto_create_CA.js"}}, "node_modules/node-opcua-pki/node_modules/async": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/async/-/async-3.2.4.tgz", "integrity": "sha512-iAB+JbDEGXhyIUavoDl9WP/Jj106Kz9DEn1DPgYw5ruDn0e3Wgi3sKFm55sASdGBNOQB8F59d9qQ7deqrHA8wQ=="}, "node_modules/node-opcua-pki/node_modules/cliui": {"version": "7.0.4", "resolved": "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz", "integrity": "sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/node-opcua-pki/node_modules/minimist": {"version": "1.2.7", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.7.tgz", "integrity": "sha512-bzfL1YUZsP41gmu/qjrEk0Q6i2ix/cVeAhbCbqH9u3zYutS1cLg00qhrD0M2MVdCcx4Sc0UpP2eBWo9rotpq6g==", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/node-opcua-pki/node_modules/yargs": {"version": "17.5.1", "resolved": "https://registry.npmjs.org/yargs/-/yargs-17.5.1.tgz", "integrity": "sha512-t6YAJcxDkNX7NFYiVtKvWUz8l+PaKTLiL63mJYWR2GnHq2gjEWISzsLp9wg3aY36dY1j+gfIEL3pIF+XlJJfbA==", "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.0.0"}, "engines": {"node": ">=12"}}, "node_modules/node-opcua-pseudo-session": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-pseudo-session/-/node-opcua-pseudo-session-2.81.0.tgz", "integrity": "sha512-p5Ql0VUR7VN/EnEICJG0J5OJUov+PSXT1ufYXDiZGfoIAusrMKhWLXzCQvk7U/XWEgObQsTUfwwfLgGxyoFtoA==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-constants": "2.77.0", "node-opcua-data-model": "2.81.0", "node-opcua-data-value": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-service-browse": "2.81.0", "node-opcua-service-call": "2.81.0", "node-opcua-service-read": "2.81.0", "node-opcua-service-subscription": "2.81.0", "node-opcua-service-translate-browse-path": "2.81.0", "node-opcua-service-write": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-types": "2.81.0", "node-opcua-variant": "2.81.0", "thenify": "^3.3.1"}}, "node_modules/node-opcua-schemas": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-schemas/-/node-opcua-schemas-2.81.0.tgz", "integrity": "sha512-QXNJ4KUHl4oV5TyNDTO/Gz7gBfKd5fcsCbwb+Na9SlQQuN60HMF7jSttQDbLcxa31jRLX6TWK3OwxQP2gZ6d+A==", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.77.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-debug": "2.77.0", "node-opcua-extension-object": "2.81.0", "node-opcua-factory": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-variant": "2.81.0", "node-opcua-xml2json": "2.77.0"}}, "node_modules/node-opcua-secure-channel": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-secure-channel/-/node-opcua-secure-channel-2.81.0.tgz", "integrity": "sha512-divsfIhurCpaPEeCo7VZM7acj7ECk+nkgPjTReG0ILYyZucJ9MaY4s8eTpVp8AEyrVoXZsBkH1pgn078SMJl9g==", "dependencies": {"async": "^3.2.4", "backoff": "^2.5.0", "chalk": "4.1.2", "node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-certificate-manager": "2.77.0", "node-opcua-chunkmanager": "2.81.0", "node-opcua-common": "2.81.0", "node-opcua-crypto": "^1.11.0", "node-opcua-debug": "2.77.0", "node-opcua-factory": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-object-registry": "2.77.0", "node-opcua-packet-analyzer": "2.81.0", "node-opcua-service-endpoints": "2.81.0", "node-opcua-service-secure-channel": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-transport": "2.81.0", "node-opcua-types": "2.81.0", "node-opcua-utils": "2.77.0"}}, "node_modules/node-opcua-secure-channel/node_modules/async": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/async/-/async-3.2.4.tgz", "integrity": "sha512-iAB+JbDEGXhyIUavoDl9WP/Jj106Kz9DEn1DPgYw5ruDn0e3Wgi3sKFm55sASdGBNOQB8F59d9qQ7deqrHA8wQ=="}, "node_modules/node-opcua-server": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-server/-/node-opcua-server-2.81.0.tgz", "integrity": "sha512-s0zdFtu6s0DVXHfMsoqbGcgqg4qbPVuQ73jxd3rUg2+IF6ll08PDZ7i0IpLQNjHU9DLZqP4x1GQxg4TZy3T8GQ==", "dependencies": {"@ster5/global-mutex": "^1.2.0", "async": "^3.2.4", "chalk": "4.1.2", "dequeue": "^1.0.5", "lodash": "4.17.21", "node-opcua-address-space": "2.81.0", "node-opcua-address-space-base": "2.81.0", "node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-certificate-manager": "2.77.0", "node-opcua-client": "2.81.0", "node-opcua-client-dynamic-extension-object": "2.81.0", "node-opcua-common": "2.81.0", "node-opcua-constants": "2.77.0", "node-opcua-crypto": "^1.11.0", "node-opcua-data-model": "2.81.0", "node-opcua-data-value": "2.81.0", "node-opcua-date-time": "2.77.0", "node-opcua-debug": "2.77.0", "node-opcua-extension-object": "2.81.0", "node-opcua-factory": "2.81.0", "node-opcua-hostname": "2.77.0", "node-opcua-nodeid": "2.81.0", "node-opcua-nodesets": "2.77.0", "node-opcua-numeric-range": "2.81.0", "node-opcua-object-registry": "2.77.0", "node-opcua-secure-channel": "2.81.0", "node-opcua-service-browse": "2.81.0", "node-opcua-service-call": "2.81.0", "node-opcua-service-discovery": "2.81.0", "node-opcua-service-endpoints": "2.81.0", "node-opcua-service-filter": "2.81.0", "node-opcua-service-history": "2.81.0", "node-opcua-service-node-management": "2.81.0", "node-opcua-service-query": "2.81.0", "node-opcua-service-read": "2.81.0", "node-opcua-service-register-node": "2.81.0", "node-opcua-service-secure-channel": "2.81.0", "node-opcua-service-session": "2.81.0", "node-opcua-service-subscription": "2.81.0", "node-opcua-service-translate-browse-path": "2.81.0", "node-opcua-service-write": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-types": "2.81.0", "node-opcua-utils": "2.77.0", "node-opcua-variant": "2.81.0", "thenify": "^3.3.1"}}, "node_modules/node-opcua-server-discovery": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-server-discovery/-/node-opcua-server-discovery-2.81.0.tgz", "integrity": "sha512-Bjmjk7z6QA/+lAKqWyKg36YtY/IeqV9TGDtMYdpF9k1JPtNEn8aOgKf/p9KTnLmRSP72Z7aNwYaUV/Oao9apYQ==", "dependencies": {"chalk": "4.1.2", "env-paths": "2.2.1", "node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-certificate-manager": "2.77.0", "node-opcua-common": "2.81.0", "node-opcua-debug": "2.77.0", "node-opcua-hostname": "2.77.0", "node-opcua-object-registry": "2.77.0", "node-opcua-secure-channel": "2.81.0", "node-opcua-server": "2.81.0", "node-opcua-service-discovery": "2.81.0", "node-opcua-service-endpoints": "2.81.0", "node-opcua-status-code": "2.77.0", "sterfive-bonjour-service": "1.1.4", "thenify": "^3.3.1"}}, "node_modules/node-opcua-server/node_modules/async": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/async/-/async-3.2.4.tgz", "integrity": "sha512-iAB+JbDEGXhyIUavoDl9WP/Jj106Kz9DEn1DPgYw5ruDn0e3Wgi3sKFm55sASdGBNOQB8F59d9qQ7deqrHA8wQ=="}, "node_modules/node-opcua-server/node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="}, "node_modules/node-opcua-service-browse": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-service-browse/-/node-opcua-service-browse-2.81.0.tgz", "integrity": "sha512-2wGj40HbDOgV183aYX5I/JhM5BNG77xd/+TOg7CM1pPE4IVkojD/Y3KgMAhaaBuEbWN0UdKsXj2nOR7Dqj9GCQ==", "dependencies": {"node-opcua-data-model": "2.81.0", "node-opcua-types": "2.81.0"}}, "node_modules/node-opcua-service-call": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-service-call/-/node-opcua-service-call-2.81.0.tgz", "integrity": "sha512-4wkv2ZWCgDF0KPGWqyqPPkdc183KRn36TKXBDsXRtkgjocCpr1FaPaULx8Mrf1EOAQGOovyH/ygBmtlyYSoc9w==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-nodeid": "2.81.0", "node-opcua-types": "2.81.0", "node-opcua-variant": "2.81.0"}}, "node_modules/node-opcua-service-discovery": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-service-discovery/-/node-opcua-service-discovery-2.81.0.tgz", "integrity": "sha512-gb1qqztdWVxjrmsk0TkLmP3WbBxeOUck6y+kW3SCrQTzY5b84w/G+einknSwJsJBFvXEYn5XO/sVliZilij4bg==", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.77.0", "node-opcua-debug": "2.77.0", "node-opcua-object-registry": "2.77.0", "node-opcua-types": "2.81.0", "sterfive-bonjour-service": "1.1.4"}}, "node_modules/node-opcua-service-endpoints": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-service-endpoints/-/node-opcua-service-endpoints-2.81.0.tgz", "integrity": "sha512-x7Kst5TqYC4ovWnPIE3Ly5g6uY3LswGa1R5KdkY/feeDWOsqpIqdWu+pkfliPXljAnMxv/QZ1sfQCrIvdE9qrg==", "dependencies": {"node-opcua-types": "2.81.0"}}, "node_modules/node-opcua-service-filter": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-service-filter/-/node-opcua-service-filter-2.81.0.tgz", "integrity": "sha512-D1w1xvQ9FxhXd6s/2VY/euZsiQ5FO0D+cVqo4Wh4CvRcePUkOz4TbBa+i1RCcuj7E2/c67mty6OTgUOd/NGWMA==", "dependencies": {"node-opcua-address-space-base": "2.81.0", "node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-constants": "2.77.0", "node-opcua-data-model": "2.81.0", "node-opcua-data-value": "2.81.0", "node-opcua-debug": "2.77.0", "node-opcua-extension-object": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-service-translate-browse-path": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-types": "2.81.0", "node-opcua-variant": "2.81.0"}}, "node_modules/node-opcua-service-history": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-service-history/-/node-opcua-service-history-2.81.0.tgz", "integrity": "sha512-mddcqojh/d6tACK85IvIE12ElyI0xZ2zY9L2iZ5h6haJ6j73mqVgFzT3OZEb3A6m5krJTz9izdeUGxgb/QSTTQ==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-data-value": "2.81.0", "node-opcua-types": "2.81.0"}}, "node_modules/node-opcua-service-node-management": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-service-node-management/-/node-opcua-service-node-management-2.81.0.tgz", "integrity": "sha512-5IiBOY0eIECzvBLilIrXy1XboJJ7v4qZ31vEZY3TscpoMrWJ/fJA8IiyIwRtbEBX8fIR8P0mkiuPjEL4Vu1Alg==", "dependencies": {"node-opcua-types": "2.81.0"}}, "node_modules/node-opcua-service-query": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-service-query/-/node-opcua-service-query-2.81.0.tgz", "integrity": "sha512-L5a+92A2n5t0edDVVdHjmdJ8sH72/eFZM4IGNcKvAZFk9mb+F5ceaZGT0ixmsYK7EWuzflqwzRKogy+zPdr9gg==", "dependencies": {"node-opcua-types": "2.81.0"}}, "node_modules/node-opcua-service-read": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-service-read/-/node-opcua-service-read-2.81.0.tgz", "integrity": "sha512-3+2NHbyuoxBPkO52P2Li5UjLCfq/bkYzBAcj+cKOJUe5otjBr00LJE0VmMGj3u7jmKDCUdOYRDDW9A1mgIExTw==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-data-model": "2.81.0", "node-opcua-data-value": "2.81.0", "node-opcua-service-secure-channel": "2.81.0", "node-opcua-types": "2.81.0"}}, "node_modules/node-opcua-service-register-node": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-service-register-node/-/node-opcua-service-register-node-2.81.0.tgz", "integrity": "sha512-WTD4tpTlkDLWbVnnsh1XyNPuJAtvMO6YyhzXmkwRsLVucIYIdYXyy1M3GjEFtywoLjkm8MvI3NFP2ONuTbJQIg==", "dependencies": {"node-opcua-types": "2.81.0"}}, "node_modules/node-opcua-service-secure-channel": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-service-secure-channel/-/node-opcua-service-secure-channel-2.81.0.tgz", "integrity": "sha512-+TOZEXaqXg9XgAXyRGDcfHhUfh1Zx9zW1BzVjqDK6DJI4uwZbpmxlVz4m18kIco53EPoQ7M9iVWLx6RJkqwLYg==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-factory": "2.81.0", "node-opcua-types": "2.81.0"}}, "node_modules/node-opcua-service-session": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-service-session/-/node-opcua-service-session-2.81.0.tgz", "integrity": "sha512-ztWpHTcBpvuBrpRr7FR4OeICEW+/e3zexrqHMG2TZmO2WgThLqqiBuj31qTI4zppYPcUEb7S8aj5bs4gwNoG3g==", "dependencies": {"node-opcua-factory": "2.81.0", "node-opcua-types": "2.81.0"}}, "node_modules/node-opcua-service-subscription": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-service-subscription/-/node-opcua-service-subscription-2.81.0.tgz", "integrity": "sha512-kaT7EZWvLrqi+bP49skUyflPZ7LCKUYH0PD8z6myR6rDgT+KRooozfZt5AHFJ4ItNI6kLaBg4jzU/DhHawo5oA==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-types": "2.81.0", "node-opcua-variant": "2.81.0"}}, "node_modules/node-opcua-service-translate-browse-path": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-service-translate-browse-path/-/node-opcua-service-translate-browse-path-2.81.0.tgz", "integrity": "sha512-0nvMKvdnDBtTKPsoKwXtTS0DN9ftxW6BDccTw9Iqt/ZYwEQe2LbUnv+gg+E1TdmFCpktiF+R2SAR1oYYt4l68Q==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-constants": "2.77.0", "node-opcua-data-model": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-types": "2.81.0"}}, "node_modules/node-opcua-service-write": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-service-write/-/node-opcua-service-write-2.81.0.tgz", "integrity": "sha512-lL6FKexyG3KpMk+ulE9n5hy5m8/U2txytbUBZjQpxeQ/hPxDgnVP2OsG+IoCW8uUDfOs3HC88RlLgmY0P106iw==", "dependencies": {"node-opcua-types": "2.81.0"}}, "node_modules/node-opcua-status-code": {"version": "2.77.0", "resolved": "https://registry.npmjs.org/node-opcua-status-code/-/node-opcua-status-code-2.77.0.tgz", "integrity": "sha512-HSjZBLz+S6bvJecTz8lKkKvz7mejPYTLEDIJ+3u03tYAhu8NGLU67fk34KqTZhypFrZ7jHvEYtbGBFGUCWLh3Q==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-binary-stream": "2.77.0"}}, "node_modules/node-opcua-transport": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-transport/-/node-opcua-transport-2.81.0.tgz", "integrity": "sha512-aVFCyVK32k0eUvRj3zcFrGCwvksIqiN2YSVKwdMM62Nn4k5A02w7qDmt1F37DsIfakffh2Tj7njz5oQfHt3z9g==", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-buffer-utils": "2.77.0", "node-opcua-chunkmanager": "2.81.0", "node-opcua-debug": "2.77.0", "node-opcua-factory": "2.81.0", "node-opcua-object-registry": "2.77.0", "node-opcua-packet-assembler": "2.77.0", "node-opcua-status-code": "2.77.0", "node-opcua-utils": "2.77.0"}}, "node_modules/node-opcua-types": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-types/-/node-opcua-types-2.81.0.tgz", "integrity": "sha512-J4Pypg9gI8NAGqQvTnwEbLPXeNZCsWvlEAfaWZni/Ts3rr/wFvq6iWX2t93MnUqPJ+MJGwPbG9OJ0615O1ftdg==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-data-model": "2.81.0", "node-opcua-data-value": "2.81.0", "node-opcua-enum": "2.77.0", "node-opcua-extension-object": "2.81.0", "node-opcua-factory": "2.81.0", "node-opcua-generator": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-numeric-range": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-variant": "2.81.0"}}, "node_modules/node-opcua-utils": {"version": "2.77.0", "resolved": "https://registry.npmjs.org/node-opcua-utils/-/node-opcua-utils-2.77.0.tgz", "integrity": "sha512-uCZ/OXVX04HwniTW5GTtJG3LcsOlyrxdd9bxh0h0in9jPrIQh1Qb/VnaXR/CAHGXpVplay2vptRQ9b1tHclAww==", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.77.0"}}, "node_modules/node-opcua-variant": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-variant/-/node-opcua-variant-2.81.0.tgz", "integrity": "sha512-zOehzcgXSSjTB1uLRIgmKgg5CXDL3u5aQPWelHPHayXUG29MjXZCc7LdnAvPdloUdXG0KRxR4UUD0tnRl6/uVg==", "dependencies": {"node-opcua-assert": "2.77.0", "node-opcua-basic-types": "2.81.0", "node-opcua-binary-stream": "2.77.0", "node-opcua-data-model": "2.81.0", "node-opcua-enum": "2.77.0", "node-opcua-factory": "2.81.0", "node-opcua-nodeid": "2.81.0", "node-opcua-utils": "2.77.0"}}, "node_modules/node-opcua-vendor-diagnostic": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/node-opcua-vendor-diagnostic/-/node-opcua-vendor-diagnostic-2.81.0.tgz", "integrity": "sha512-DZ7Og9tCOR2L2pERwaeVO46BHz+0L7rncO+O8239wLTpJGDBVbdTQgIUV/Fwnfl7YcW2qpD7hHQR/zLUpVFBQg==", "dependencies": {"humanize": "0.0.9", "node-opcua-address-space": "2.81.0", "node-opcua-assert": "2.77.0", "node-opcua-constants": "2.77.0", "node-opcua-server": "2.81.0", "node-opcua-status-code": "2.77.0", "node-opcua-variant": "2.81.0"}}, "node_modules/node-opcua-xml2json": {"version": "2.77.0", "resolved": "https://registry.npmjs.org/node-opcua-xml2json/-/node-opcua-xml2json-2.77.0.tgz", "integrity": "sha512-hpx9LtCAc1fiUZhtLGtl76p/BRjjYqtNQcLny76ov+DiieUvFUhWzMLSLSPksQ5HR3aUZROO7cOXXZMPw+ahPA==", "dependencies": {"ltx": "^3.0.0", "node-opcua-assert": "2.77.0", "node-opcua-utils": "2.77.0", "thenify": "^3.3.1", "xml-writer": "^1.7.0"}}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==", "engines": {"node": ">=0.10.0"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/on-finished": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "dev": true, "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz", "integrity": "sha512-pZA<PERSON>+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dependencies": {"wrappy": "1"}}, "node_modules/ordered-read-streams": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/ordered-read-streams/-/ordered-read-streams-1.0.1.tgz", "integrity": "sha1-d8DLN8QVJdZBZtmQ/61+xqDhNj4=", "dev": true, "dependencies": {"readable-stream": "^2.0.1"}}, "node_modules/pako": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/pako/-/pako-1.0.10.tgz", "integrity": "sha512-0DTvPVU3ed8+HNXOu5Bs+o//Mbdj9VNQMUOe9oKCwh8l0GNwpTDMKCWbRjgtD291AWnkAgkqA/LOnQS8AmS1tw==", "dev": true}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/path-dirname": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/path-dirname/-/path-dirname-1.0.2.tgz", "integrity": "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=", "dev": true}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/path-parse": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.6.tgz", "integrity": "sha512-GSmOT2EbHrINBf9SR7CDELwlJ8AENk3Qn7OikK4nFYAu3Ote2+JYNVvkpAEQm3/TLNEJFD/xZJjzyxg3KBWOzw==", "dev": true}, "node_modules/path-to-regexp": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=", "dev": true}, "node_modules/pend": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz", "integrity": "sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg=="}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/precond": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/precond/-/precond-0.2.3.tgz", "integrity": "sha512-QCYG84SgGyGzqJ/vlMsxeXd/pgL/I94ixdNFyh1PusWmTCyVfPJjZ1K1jvHtsbfnXQs2TSkEP2fR7QiMZAnKFQ==", "engines": {"node": ">= 0.6"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==", "dev": true}, "node_modules/progress": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz", "integrity": "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==", "engines": {"node": ">=0.4.0"}}, "node_modules/promise": {"version": "7.3.1", "resolved": "https://registry.npmjs.org/promise/-/promise-7.3.1.tgz", "integrity": "sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==", "dev": true, "dependencies": {"asap": "~2.0.3"}}, "node_modules/proxy-addr": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.5.tgz", "integrity": "sha512-t/7RxHXPH6cJtP0pRG6smSr9QJidhB+3kXu0KgXnbGYMgzEnUxRQ4/LDdfOwZEMyIh3/xHb8PX3t+lfL9z+YVQ==", "dev": true, "dependencies": {"forwarded": "~0.1.2", "ipaddr.js": "1.9.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/pseudomap": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/pseudomap/-/pseudomap-1.0.2.tgz", "integrity": "sha1-8FKijacOYYkX7wqKw0wa5aaChrM=", "dev": true}, "node_modules/pug": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/pug/-/pug-2.0.4.tgz", "integrity": "sha512-XhoaDlvi6NIzL49nu094R2NA6P37ijtgMDuWE+ofekDChvfKnzFal60bhSdiy8y2PBO6fmz3oMEIcfpBVRUdvw==", "dev": true, "dependencies": {"pug-code-gen": "^2.0.2", "pug-filters": "^3.1.1", "pug-lexer": "^4.1.0", "pug-linker": "^3.0.6", "pug-load": "^2.0.12", "pug-parser": "^5.0.1", "pug-runtime": "^2.0.5", "pug-strip-comments": "^1.0.4"}}, "node_modules/pug-attrs": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/pug-attrs/-/pug-attrs-2.0.4.tgz", "integrity": "sha512-TaZ4Z2TWUPDJcV3wjU3RtUXMrd3kM4Wzjbe3EWnSsZPsJ3LDI0F3yCnf2/W7PPFF+edUFQ0HgDL1IoxSz5K8EQ==", "dev": true, "dependencies": {"constantinople": "^3.0.1", "js-stringify": "^1.0.1", "pug-runtime": "^2.0.5"}}, "node_modules/pug-code-gen": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/pug-code-gen/-/pug-code-gen-2.0.2.tgz", "integrity": "sha512-kROFWv/AHx/9CRgoGJeRSm+4mLWchbgpRzTEn8XCiwwOy6Vh0gAClS8Vh5TEJ9DBjaP8wCjS3J6HKsEsYdvaCw==", "dev": true, "dependencies": {"constantinople": "^3.1.2", "doctypes": "^1.1.0", "js-stringify": "^1.0.1", "pug-attrs": "^2.0.4", "pug-error": "^1.3.3", "pug-runtime": "^2.0.5", "void-elements": "^2.0.1", "with": "^5.0.0"}}, "node_modules/pug-error": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/pug-error/-/pug-error-1.3.3.tgz", "integrity": "sha512-qE3YhESP2mRAWMFJgKdtT5D7ckThRScXRwkfo+Erqga7dyJdY3ZquspprMCj/9sJ2ijm5hXFWQE/A3l4poMWiQ==", "dev": true}, "node_modules/pug-filters": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/pug-filters/-/pug-filters-3.1.1.tgz", "integrity": "sha512-lFfjNyGEyVWC4BwX0WyvkoWLapI5xHSM3xZJFUhx4JM4XyyRdO8Aucc6pCygnqV2uSgJFaJWW3Ft1wCWSoQkQg==", "dev": true, "dependencies": {"clean-css": "^4.1.11", "constantinople": "^3.0.1", "jstransformer": "1.0.0", "pug-error": "^1.3.3", "pug-walk": "^1.1.8", "resolve": "^1.1.6", "uglify-js": "^2.6.1"}}, "node_modules/pug-lexer": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/pug-lexer/-/pug-lexer-4.1.0.tgz", "integrity": "sha512-i55yzEBtjm0mlplW4LoANq7k3S8gDdfC6+LThGEvsK4FuobcKfDAwt6V4jKPH9RtiE3a2Akfg5UpafZ1OksaPA==", "dev": true, "dependencies": {"character-parser": "^2.1.1", "is-expression": "^3.0.0", "pug-error": "^1.3.3"}}, "node_modules/pug-linker": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/pug-linker/-/pug-linker-3.0.6.tgz", "integrity": "sha512-bagfuHttfQOpANGy1Y6NJ+0mNb7dD2MswFG2ZKj22s8g0wVsojpRlqveEQHmgXXcfROB2RT6oqbPYr9EN2ZWzg==", "dev": true, "dependencies": {"pug-error": "^1.3.3", "pug-walk": "^1.1.8"}}, "node_modules/pug-load": {"version": "2.0.12", "resolved": "https://registry.npmjs.org/pug-load/-/pug-load-2.0.12.tgz", "integrity": "sha512-UqpgGpyyXRYgJs/X60sE6SIf8UBsmcHYKNaOccyVLEuT6OPBIMo6xMPhoJnqtB3Q3BbO4Z3Bjz5qDsUWh4rXsg==", "dev": true, "dependencies": {"object-assign": "^4.1.0", "pug-walk": "^1.1.8"}}, "node_modules/pug-parser": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/pug-parser/-/pug-parser-5.0.1.tgz", "integrity": "sha512-nGHqK+w07p5/PsPIyzkTQfzlYfuqoiGjaoqHv1LjOv2ZLXmGX1O+4Vcvps+P4LhxZ3drYSljjq4b+Naid126wA==", "dev": true, "dependencies": {"pug-error": "^1.3.3", "token-stream": "0.0.1"}}, "node_modules/pug-runtime": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/pug-runtime/-/pug-runtime-2.0.5.tgz", "integrity": "sha512-P+rXKn9un4fQY77wtpcuFyvFaBww7/91f3jHa154qU26qFAnOe6SW1CbIDcxiG5lLK9HazYrMCCuDvNgDQNptw==", "dev": true}, "node_modules/pug-strip-comments": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/pug-strip-comments/-/pug-strip-comments-1.0.4.tgz", "integrity": "sha512-i5j/9CS4yFhSxHp5iKPHwigaig/VV9g+FgReLJWWHEHbvKsbqL0oP/K5ubuLco6Wu3Kan5p7u7qk8A4oLLh6vw==", "dev": true, "dependencies": {"pug-error": "^1.3.3"}}, "node_modules/pug-walk": {"version": "1.1.8", "resolved": "https://registry.npmjs.org/pug-walk/-/pug-walk-1.1.8.tgz", "integrity": "sha512-GMu3M5nUL3fju4/egXwZO0XLi6fW/K3T3VTgFQ14GxNi8btlxgT5qZL//JwZFm/2Fa64J/PNS8AZeys3wiMkVA==", "dev": true}, "node_modules/pump": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/pump/-/pump-3.0.0.tgz", "integrity": "sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==", "dev": true, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/pumpify": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/pumpify/-/pumpify-1.5.1.tgz", "integrity": "sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ==", "dev": true, "dependencies": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}}, "node_modules/pumpify/node_modules/pump": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/pump/-/pump-2.0.1.tgz", "integrity": "sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA==", "dev": true, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz", "integrity": "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=", "dev": true}, "node_modules/qs": {"version": "6.7.0", "resolved": "https://registry.npmjs.org/qs/-/qs-6.7.0.tgz", "integrity": "sha512-VCdBRNFTX1fyE7Nb6FYoURo/SPe62QCaAyzJvUjwRaIsc+NePBEniHlvxFmmX56+HZphIGtV0XeCirBtpDrTyQ==", "dev": true, "engines": {"node": ">=0.6"}}, "node_modules/querystring": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/querystring/-/querystring-0.2.0.tgz", "integrity": "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=", "deprecated": "The querystring API is considered Legacy. new code should use the URLSearchParams API instead.", "dev": true, "engines": {"node": ">=0.4.x"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.4.0.tgz", "integrity": "sha512-4Oz8DUIwdvoa5qMJelxipzi/iJIi40O5cGV1wNYp5hvZP8ZN0T+jiNkL0QepXs+EsQ9XJ8ipEDoiH70ySUJP3Q==", "dev": true, "dependencies": {"bytes": "3.1.0", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/readable-stream": {"version": "2.3.6", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw==", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/readdirp": {"version": "3.6.0", "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz", "integrity": "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/regenerator-runtime": {"version": "0.11.1", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz", "integrity": "sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==", "dev": true}, "node_modules/reinterval": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/reinterval/-/reinterval-1.1.0.tgz", "integrity": "sha1-M2Hs+jymwYKDOA3Qu5VG85D17Oc=", "dev": true}, "node_modules/remove-trailing-separator": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "dev": true}, "node_modules/repeat-string": {"version": "1.6.1", "resolved": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc=", "dev": true, "engines": {"node": ">=0.10"}}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "engines": {"node": ">=0.10.0"}}, "node_modules/resolve": {"version": "1.12.0", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.12.0.tgz", "integrity": "sha512-B/dOmuoAik5bKcD6s6nXDCjzUKnaDvdkRyAk6rsmsKLipWj4797iothd7jmmUhWTfinVMU+wc56rYKsit2Qy4w==", "dev": true, "dependencies": {"path-parse": "^1.0.6"}}, "node_modules/right-align": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/right-align/-/right-align-0.1.3.tgz", "integrity": "sha1-YTObci/mo1FWiSENJOFMlhSGE+8=", "dev": true, "dependencies": {"align-text": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==", "dev": true}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}, "node_modules/sax": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/sax/-/sax-1.2.1.tgz", "integrity": "sha1-e45lYZCyKOgaZq6nSEgNgozS03o=", "dev": true}, "node_modules/seed-random": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/seed-random/-/seed-random-2.2.0.tgz", "integrity": "sha1-KpsZ4lCoFwmSMaW5mk2vgLf77VQ=", "dev": true}, "node_modules/semver": {"version": "7.3.8", "resolved": "https://registry.npmjs.org/semver/-/semver-7.3.8.tgz", "integrity": "sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/semver/node_modules/lru-cache": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/semver/node_modules/yallist": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="}, "node_modules/send": {"version": "0.17.1", "resolved": "https://registry.npmjs.org/send/-/send-0.17.1.tgz", "integrity": "sha512-BsVKsiGcQMFwT8UxypobUKyv7irCNRHk1T0G680vk88yf6LBByGcZJOTJCrTP2xVN6yI+XjPJcNuE3V4fT9sAg==", "dev": true, "dependencies": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.7.2", "mime": "1.6.0", "ms": "2.1.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1", "statuses": "~1.5.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/ms": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.1.tgz", "integrity": "sha512-tgp+dl5cGk28utYktBsrFqA7HKgrhgPsg6Z/EfhWI4gl1Hwq8B/GmY/0oXZ6nF8hDVesS/FpnYaD/kOWhYQvyg==", "dev": true}, "node_modules/seq-queue": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/seq-queue/-/seq-queue-0.0.5.tgz", "integrity": "sha1-1WgS4cAXpuTnw+Ojeh2m143TyT4=", "dev": true}, "node_modules/serve-favicon": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/serve-favicon/-/serve-favicon-2.5.0.tgz", "integrity": "sha1-k10kDN/g9YBTB/3+ln2IlCosvPA=", "dev": true, "dependencies": {"etag": "~1.8.1", "fresh": "0.5.2", "ms": "2.1.1", "parseurl": "~1.3.2", "safe-buffer": "5.1.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-favicon/node_modules/ms": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.1.tgz", "integrity": "sha512-tgp+dl5cGk28utYktBsrFqA7HKgrhgPsg6Z/EfhWI4gl1Hwq8B/GmY/0oXZ6nF8hDVesS/FpnYaD/kOWhYQvyg==", "dev": true}, "node_modules/serve-favicon/node_modules/safe-buffer": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.1.tgz", "integrity": "sha512-kKvNJn6Mm93gAczWVJg7wH+wGYWNrDHdWvpUmHyEsgCtIwwo3bqPtV4tR5tuPaUhTOo/kvhVwd8XwwOllGYkbg==", "dev": true}, "node_modules/serve-static": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/serve-static/-/serve-static-1.14.1.tgz", "integrity": "sha512-JMrvUwE54emCYWlTI+hGrGv5I8dEwmco/00EvkzIIsR7MqrHonbD9pO2MOfFnpFntl7ecpZs+3mW+XbQZu9QCg==", "dev": true, "dependencies": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.17.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/set-immediate-shim": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/set-immediate-shim/-/set-immediate-shim-1.0.1.tgz", "integrity": "sha1-SysbJ+uAip+NzEgaWOXlb1mfP2E=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/set-interval-async": {"version": "1.0.29", "resolved": "https://registry.npmjs.org/set-interval-async/-/set-interval-async-1.0.29.tgz", "integrity": "sha512-OjR8iFWOx9RJC+A+JAmFHZyOABBRriO8RPEbZ+kmlRbv4Bd25R4AaDd0NLqHayErDHHd2nQwX1AgNauhil62lQ==", "dev": true, "dependencies": {"@babel/runtime": "7.5.0"}}, "node_modules/set-prototype-of": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/set-prototype-of/-/set-prototype-of-1.0.0.tgz", "integrity": "sha512-OeTRSF+prexqa0ZOjfYR2pdGG/9nyzoXhsDj9M/0R8cgK1r9SkiQiqGdQQcObmnalKVPaTLrF8P71OacYqcYGw=="}, "node_modules/setprototypeof": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.1.tgz", "integrity": "sha512-JvdAWfbXeIGaZ9cILp38HntZSFSo3mWg6xGcJJsd+d4aRMOqauag1C63dJfDw7OaMYwEbHMOxEZ1lqVRYP2OAw==", "dev": true}, "node_modules/source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/split2": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/split2/-/split2-3.1.1.tgz", "integrity": "sha512-emNzr1s7ruq4N+1993yht631/JH+jaj0NYBosuKmLcq+JkGQ9MmTw1RB1fGaTCzUuseRIClrlSLHRNYGwWQ58Q==", "dev": true, "dependencies": {"readable-stream": "^3.0.0"}}, "node_modules/split2/node_modules/readable-stream": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.4.0.tgz", "integrity": "sha512-jItXPLmrSR8jmTRmRWJXCnGJsfy85mB3Wd/uINMXA65yrnFo0cPClFIUWzo2najVNSl+mx7/4W8ttlLWJe99pQ==", "dev": true, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/sqlstring": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/sqlstring/-/sqlstring-2.3.1.tgz", "integrity": "sha1-R1OT/56RR5rqYtyvDKPRSYOn+0A=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/sshpk": {"version": "1.17.0", "resolved": "https://registry.npmjs.org/sshpk/-/sshpk-1.17.0.tgz", "integrity": "sha512-/9HIEs1ZXGhSPE8X6Ccm7Nam1z8KcoCqPdI7ecm1N33EzAetWahvQWVqLZtaZQ+IDKX4IyA2o0gBzqIMkAagHQ==", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "node_modules/statuses": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/sterfive-bonjour-service": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/sterfive-bonjour-service/-/sterfive-bonjour-service-1.1.4.tgz", "integrity": "sha512-QqDpnBb3KLD6ytdY2KSxsynw1jJAvzfOloQt83GQNXO6CGf84ZY+37tpOEZo1FzgUkFiVsL7pYyg71olDppI/w==", "dependencies": {"@types/multicast-dns": "^7.2.1", "array-flatten": "^2.1.2", "dns-equal": "^1.0.0", "fast-deep-equal": "^3.1.3", "multicast-dns": "^7.2.4"}}, "node_modules/sterfive-bonjour-service/node_modules/array-flatten": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/array-flatten/-/array-flatten-2.1.2.tgz", "integrity": "sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ=="}, "node_modules/stream-shift": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/stream-shift/-/stream-shift-1.0.0.tgz", "integrity": "sha1-1cdSgl5TZ+eG944Y5EXqIjoVWVI=", "dev": true}, "node_modules/streamsearch": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/streamsearch/-/streamsearch-0.1.2.tgz", "integrity": "sha1-gIudDlb8Jz2Am6VzOOkpkZoanxo=", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "dev": true, "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/tar-stream": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/tar-stream/-/tar-stream-2.1.0.tgz", "integrity": "sha512-+DAn4Nb4+gz6WZigRzKEZl1QuJVOLtAwwF+WUxy1fJ6X63CaGaUAxJRD2KEn1OMfcbCjySTYpNC6WmfQoIEOdw==", "dev": true, "dependencies": {"bl": "^3.0.0", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}}, "node_modules/tar-stream/node_modules/bl": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/bl/-/bl-3.0.0.tgz", "integrity": "sha512-EUAyP5UHU5hxF8BPT0LKW8gjYLhq1DQIcneOX/pL/m2Alo+OYDQAJlHq+yseMP50Os2nHXOSic6Ss3vSQeyf4A==", "dev": true, "dependencies": {"readable-stream": "^3.0.1"}}, "node_modules/tar-stream/node_modules/readable-stream": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.4.0.tgz", "integrity": "sha512-jItXPLmrSR8jmTRmRWJXCnGJsfy85mB3Wd/uINMXA65yrnFo0cPClFIUWzo2najVNSl+mx7/4W8ttlLWJe99pQ==", "dev": true, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/thenify": {"version": "3.3.1", "resolved": "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz", "integrity": "sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==", "dependencies": {"any-promise": "^1.0.0"}}, "node_modules/through2": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/through2/-/through2-2.0.5.tgz", "integrity": "sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ==", "dev": true, "dependencies": {"readable-stream": "~2.3.6", "xtend": "~4.0.1"}}, "node_modules/through2-filter": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/through2-filter/-/through2-filter-3.0.0.tgz", "integrity": "sha512-jaRjI2WxN3W1V8/FMZ9HKIBXixtiqs3SQSX4/YGIiP3gL6djW48VoZq9tDqeCWs3MT8YY5wb/zli8VW8snY1CA==", "dev": true, "dependencies": {"through2": "~2.0.0", "xtend": "~4.0.0"}}, "node_modules/thunky": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/thunky/-/thunky-1.1.0.tgz", "integrity": "sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA=="}, "node_modules/tiny-emitter": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-2.1.0.tgz", "integrity": "sha512-NB6Dk1A9xgQPMoGqC5CVXn123gWyte215ONT5Pp5a0yt4nlEoO1ZWeCwpncaekPHXO60i47ihFnZPiRPjRMq4Q==", "dev": true}, "node_modules/to-absolute-glob": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/to-absolute-glob/-/to-absolute-glob-2.0.2.tgz", "integrity": "sha1-GGX0PZ50sIItufFFt4z/fQ98hJs=", "dev": true, "dependencies": {"is-absolute": "^1.0.0", "is-negated-glob": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-fast-properties": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-1.0.3.tgz", "integrity": "sha1-uDVx+k2MJbguIxsG46MFXeTKGkc=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.0.tgz", "integrity": "sha512-yaOH/Pk/VEhBWWTlhI+qXxDFXlejDGcQipMlyxda9nthulaxLZUNcUqFxokp0vcYnvteJln5FNQDRrxj3YcbVw==", "dev": true, "engines": {"node": ">=0.6"}}, "node_modules/token-stream": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/token-stream/-/token-stream-0.0.1.tgz", "integrity": "sha1-zu78cXp2xDFvEm0LnbqlXX598Bo=", "dev": true}, "node_modules/tunnel": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/tunnel/-/tunnel-0.0.6.tgz", "integrity": "sha512-1h/Lnq9yajKY2PEbBadPXj3VxsDDu844OnaAo52UVmIzIvwwtBPIuNvkjuzBlTWpfJyUbG3ez0KSBibQkj4ojg==", "engines": {"node": ">=0.6.11 <=0.7.0 || >=0.7.3"}}, "node_modules/tweetnacl": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA=="}, "node_modules/type": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/type/-/type-1.0.3.tgz", "integrity": "sha512-51IMtNfVcee8+9GJvj0spSuFcZHe9vSib6Xtgsny1Km9ugyz2mbS08I3rsUIRYgJohFRFU1160sgRodYz378Hg==", "dev": true}, "node_modules/type-is": {"version": "1.6.18", "resolved": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "integrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==", "dev": true, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typed-function": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/typed-function/-/typed-function-1.1.1.tgz", "integrity": "sha512-RbN7MaTQBZLJYzDENHPA0nUmWT0Ex80KHItprrgbTPufYhIlTePvCXZxyQK7wgn19FW5bnuaBIKcBb5mRWjB1Q==", "dev": true, "engines": {"node": ">= 6"}}, "node_modules/typedarray": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=", "dev": true}, "node_modules/uglify-js": {"version": "2.8.29", "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.8.29.tgz", "integrity": "sha1-KcVzMUgFe7Th913zW3qcty5qWd0=", "dev": true, "dependencies": {"source-map": "~0.5.1", "yargs": "~3.10.0"}, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}, "optionalDependencies": {"uglify-to-browserify": "~1.0.0"}}, "node_modules/uglify-js/node_modules/source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/uglify-to-browserify": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz", "integrity": "sha1-bgkk1r2mta/jSeOabWMoUKD4grc=", "dev": true, "optional": true}, "node_modules/ultron": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ultron/-/ultron-1.1.1.tgz", "integrity": "sha512-UIEXBNeYmKptWH6z8ZnqTeS8fV74zG0/eRU9VGkpzz+LIJNs8W/zM/L+7ctCkRrgbNnnR0xxw4bKOr0cW0N0Og==", "dev": true}, "node_modules/unc-path-regex": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/unc-path-regex/-/unc-path-regex-0.1.2.tgz", "integrity": "sha1-5z3T17DXxe2G+6xrCufYxqadUPo=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/underscore": {"version": "1.13.6", "resolved": "https://registry.npmjs.org/underscore/-/underscore-1.13.6.tgz", "integrity": "sha512-+A5Sja4HP1M08MaXya7p5LvjuM7K6q/2EaC0+iovj/wOcMsTzMvDFbasi/oSapiwOlt252IqsKqPjCl7huKS0A=="}, "node_modules/unique-stream": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/unique-stream/-/unique-stream-2.3.1.tgz", "integrity": "sha512-2nY4TnBE70yoxHkDli7DMazpWiP7xMdCYqU2nBRO0UB+ZpEkGsSija7MvmvnZFUeC+mrgiUfcHSr3LmRFIg4+A==", "dev": true, "dependencies": {"json-stable-stringify-without-jsonify": "^1.0.1", "through2-filter": "^3.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/url": {"version": "0.10.3", "resolved": "https://registry.npmjs.org/url/-/url-0.10.3.tgz", "integrity": "sha1-Ah5NnHcF8hu/N9A861h2dAJ3TGQ=", "dev": true, "dependencies": {"punycode": "1.3.2", "querystring": "0.2.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true}, "node_modules/utils-merge": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=", "dev": true, "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-3.3.2.tgz", "integrity": "sha512-yXJmeNaw3DnnKAOKJE51sL/ZaYfWJRl1pK9dr19YFCu0ObS231AB1/LbqTKRAQ5kw8A90rA6fr4riOUpTZvQZA==", "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "dev": true, "bin": {"uuid": "bin/uuid"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/void-elements": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/void-elements/-/void-elements-2.0.1.tgz", "integrity": "sha1-wGavtYK7HLQSjWDqkjkulNXp2+w=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/websocket-stream": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/websocket-stream/-/websocket-stream-5.5.0.tgz", "integrity": "sha512-EXy/zXb9kNHI07TIMz1oIUIrPZxQRA8aeJ5XYg5ihV8K4kD1DuA+FY6R96HfdIHzlSzS8HiISAfrm+vVQkZBug==", "dev": true, "dependencies": {"duplexify": "^3.5.1", "inherits": "^2.0.1", "readable-stream": "^2.3.3", "safe-buffer": "^5.1.2", "ws": "^3.2.0", "xtend": "^4.0.0"}}, "node_modules/wget-improved-2": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/wget-improved-2/-/wget-improved-2-3.3.0.tgz", "integrity": "sha512-NSPde/8mUqgmznPhO7oB5gS8IVUlR7GOlY857IaAf3PkkHbx/6FwZxUhW+GRP1GQbZDnCMF5fPieWXFng8Z43A==", "dependencies": {"minimist": "1.2.6", "tunnel": "0.0.6"}, "bin": {"nwget": "bin/nwget"}, "engines": {"node": ">= 0.6.18"}}, "node_modules/wget-improved-2/node_modules/minimist": {"version": "1.2.6", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.6.tgz", "integrity": "sha512-Jsjnk4bw3YJqYzbdyBiNsPWHPfO++UGG749Cxs6peCu5Xg4nrena6OVxOYxrQTqww0Jmwt+Ref8rggumkTLz9Q=="}, "node_modules/window-size": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/window-size/-/window-size-0.1.0.tgz", "integrity": "sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0=", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/with": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/with/-/with-5.1.1.tgz", "integrity": "sha1-+k2qktrzLE6pTtRTyB8EaGtXXf4=", "dev": true, "dependencies": {"acorn": "^3.1.0", "acorn-globals": "^3.0.0"}}, "node_modules/wordwrap": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz", "integrity": "sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "node_modules/ws": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/ws/-/ws-3.3.3.tgz", "integrity": "sha512-nnWLa/NwZSt4KQJu51MYlCcSQ5g7INpOrOMt4XV8j4dqTXdmlUmSHQ8/oLC069ckre0fRsgfvsKwbTdtKLCDkA==", "dev": true, "dependencies": {"async-limiter": "~1.0.0", "safe-buffer": "~5.1.0", "ultron": "~1.1.0"}}, "node_modules/xml-writer": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/xml-writer/-/xml-writer-1.7.0.tgz", "integrity": "sha512-elFVMRiV5jb59fbc87zzVa0C01QLBEWP909mRuWqFqrYC5wNTH5QW4AaKMNv7d6zAsuOulkD7wnztZNLQW0Nfg==", "engines": {"node": ">=0.4.0"}}, "node_modules/xml2js": {"version": "0.4.19", "resolved": "https://registry.npmjs.org/xml2js/-/xml2js-0.4.19.tgz", "integrity": "sha512-esZnJZJOiJR9wWKMyuvSE1y6Dq5LCuJanqhxslH2bxM6duahNZ+HMpCLhBQGZkbX6xRf8x1Y2eJlgt2q3qo49Q==", "dev": true, "dependencies": {"sax": ">=0.6.0", "xmlbuilder": "~9.0.1"}}, "node_modules/xmlbuilder": {"version": "9.0.7", "resolved": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-9.0.7.tgz", "integrity": "sha1-Ey7mPS7FVlxVfiD0wi35rKaGsQ0=", "dev": true, "engines": {"node": ">=4.0"}}, "node_modules/xtend": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz", "integrity": "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==", "dev": true, "engines": {"node": ">=0.4"}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.0.3.tgz", "integrity": "sha512-S+Zk8DEWE6oKpV+vI3qWkaK+jSbIK86pCwe2IF/xwIpQ8jEuxpw9NyaGjmp9+BoJv5FV2piqCDcoCtStppiq2A==", "dev": true}, "node_modules/yargs": {"version": "3.10.0", "resolved": "https://registry.npmjs.org/yargs/-/yargs-3.10.0.tgz", "integrity": "sha1-9+572FfdfB0tOMDnTvvWgdFDH9E=", "dev": true, "dependencies": {"camelcase": "^1.0.2", "cliui": "^2.1.0", "decamelize": "^1.0.0", "window-size": "0.1.0"}}, "node_modules/yargs-parser": {"version": "21.1.1", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==", "engines": {"node": ">=12"}}, "node_modules/yauzl": {"version": "2.10.0", "resolved": "https://registry.npmjs.org/yauzl/-/yauzl-2.10.0.tgz", "integrity": "sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g==", "dependencies": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}}, "node_modules/zip-stream": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/zip-stream/-/zip-stream-0.5.0.tgz", "integrity": "sha1-Sf3/mq8nzwk4971X4nX0TmS3TCg=", "dev": true, "dependencies": {"compress-commons": "~0.2.0", "lodash": "~2.4.1", "readable-stream": "~1.0.26"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/zip-stream/node_modules/isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "node_modules/zip-stream/node_modules/lodash": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha1-+t2DS5aDBz2hebPq5tnA0VBT9z4=", "dev": true, "engines": ["node", "rhino"]}, "node_modules/zip-stream/node_modules/readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/zip-stream/node_modules/string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=", "dev": true}}}