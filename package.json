{"name": "send-message", "version": "1.0.0", "description": "sendmessage example for WebSockets on API Gateway", "main": "src/app.js", "author": "SAM CLI", "license": "MIT", "dependencies": {"form-data": "^4.0.0", "node-opcua": "^2.81.0"}, "devDependencies": {"archiver": "^3.1.1", "aws-sdk": "^2.739.0", "body-parser": "^1.15.0", "cookie-parser": "^1.4.0", "debug": "^2.2.0", "express": "^4.14.0", "express-zip": "^2.0.1", "formidable": "^1.2.1", "https": "^1.0.0", "influx": "^5.4.1", "jszip": "^3.2.2", "mathjs": "^6.2.2", "moment": "^2.24.0", "morgan": "^1.7.0", "mqtt": "^3.0.0", "multer": "^1.4.2", "mysql": "^2.17.1", "mysql2": "^1.7.0", "pug": "^2.0.0-beta6", "serve-favicon": "^2.3.0", "set-interval-async": "^1.0.29", "modbusjs": "^0.0.11"}}