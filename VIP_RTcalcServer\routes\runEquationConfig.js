var express = require('express');
var router = express.Router();
const helperFunctions = require("./dashboardInternalAPIFunctions.js")


router.post('/', function (req, res) { // runs calcs only
    console.log('run POST on runEquationConfig');
    try {
        helperFunctions.recieveUnzipAndCompute(helperFunctions.run, req, res, {isWriteBack: false}).parse(req);
    } catch (e) {
    	let msg = 'ERROR: runEquationConfig.js run POST - ' + e.message;
        console.log(msg);
        res.end(msg)
    }
})

module.exports = router;