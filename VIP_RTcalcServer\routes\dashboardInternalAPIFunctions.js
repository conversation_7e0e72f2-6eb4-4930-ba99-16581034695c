const path = require('path');
var express = require('express');
var moment = require("moment");
var router = express.Router();
var zip = require('express-zip');
var fs = require('fs');
var util = require('util');
var jszip = require("jszip");
const archiver = require('archiver')
var multer = require('multer');
var upload = multer();
var formidable = require('formidable');
var os = require('os');
const startRTcalculation = require("../.././CalculationFunctions.js")

var calcSetupFolder =  __dirname+'/../../liveConfigs';
var configVersionsFolder =  __dirname+'/../../rtCalcConfigArchive';
var archiverTemp =  __dirname+'/../../archiverTemp';
var fileUniqeID = 0

function recieveUnzipAndCompute (task, req, res, callbackParams = {}) {
	var error = false;
	var form = new formidable.IncomingForm();
    var files = [];
    var fields = {};
    var msg = "";

    form.uploadDir = os.tmpdir();
    form.on('field', function (field, value) {
        fields[field] = value;
    }).on('file', function (field, file) {
        files.push([field, file]);
    }).on('end', function () {
    	msg = 'Upload received'
        console.log(msg);
        files.forEach(function (element) {
        	fs.readFile(element[1].path, function (err, data) {
				if (err) {
			        error = true;
			        msg = "Error reading recieved files: "+err;
			    } else {
			    	jszip.loadAsync(data).then(function (unzippedFiles) {
			        	task(unzippedFiles, req, res, error, callbackParams)
					}).catch((err) => {
						error = true;
			            msg = "Error unzipping using jszip: "+err;
			            console.log(msg);
			            res.end(msg)
			        });
				}
			});
        })
	});

	return form
}

async function run (unzippedFiles, req, res, error, callbackParams) { //saves configs on server and saves result if isWriteBack is true
	let files = Object.keys(unzippedFiles.files)
	let msg = "";
	let configs = new Map();
	let tempFileName = "tempFile_"+fileUniqeID+"_"+moment().unix().toString()
	let fileLoc = archiverTemp+"/"+tempFileName+".zip"
	let pageName
	let parsedData

	fileUniqeID += fileUniqeID
	await new Promise((resolve, reject) => {
		unzippedFiles.file(files[0]).async("string").then(function (data) {
			parsedData = JSON.parse(data)
	        pageName = parsedData["Dashboard"];
	        configs.set(pageName.replace(/\s+/g, ''), parsedData)
	        resolve()
    	})
	})

	let calcResults = await new Promise ((resolve, reject) => {
		resolve(startRTcalculation(callbackParams.isWriteBack, true, true, pageName, parsedData, true)) // (isWriteBack , isLogReturned , isDataReturned , configs = configsMap.entries())
	}) 

	let zipFile = jszip()
	zipFile.file("send.zip", JSON.stringify(calcResults));
	zipFile.generateAsync({type:"base64", compression: "DEFLATE", compressionOptions: {level: 9}}).then(async function(content) {
		content = {
	        batchNo: 0,
	        batchSize: 1,
	        batch: content,
	        execId: parsedData.execId
	    }

	    content = JSON.stringify(content)
		await new Promise ((resolve, reject) => {
			fs.appendFile(fileLoc , content, function (err) {resolve()})
		})
		// res.writeHead(error ? 400 : 200 , { 'content-type': 'text/plain', 'Access-Control-Allow-Origin': '*' })
		res.header('content-type', 'text/plain')
		res.header('Access-Control-Allow-Origin', '*' )
		res.sendFile(path.resolve(fileLoc))
		res.on('finish', function () {
			res.end()
			fs.unlink(fileLoc, (err => {
                if (err) { console.log(err)}
            }))
        })
	});
}

module.exports = {
	recieveUnzipAndCompute: recieveUnzipAndCompute,
	run: run
}