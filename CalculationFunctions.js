var https = require('https')
var fs = require('fs');
const path = require('path');
var moment = require("moment");
var util = require('util');
var config = require('./config.json');
var mqtt = require("mqtt");
var mathjs = require('mathjs');
var influx = require('influx');
var mysql = require('mysql');
var opcUA = require('node-opcua');
const ModbusTcpClient = require('modbusjs').ModbusTcpClient;
var externalConfig = fs.readFileSync(__dirname+'/externalConfig.json')
const contLogsFolder = __dirname+"/contLogs/"
externalConfig = JSON.parse(externalConfig)

// read either from opc or influx, both together is not supported and may not be stable
const opcUAWriteEnabled = false
const opcUAReadEnabled = false 
const influxQueriesEnabled = true
var modbusWriteEnabled = true;

//var opServerOptions = config.opServerOptions;
var calctimer = null;
var allowedWriteDataBases = [
        "BishaTime",
        "BishaAnalytic"
    ];
/**********************************************************************************************************************************/
var mathjsFuncIgnoreList = ["compile","evaluate","help","parser","lsolve","lsolveAll","lup","lusolve","qr","slu","usolve","usolveAll","abs","add","cbrt","ceil","cube","divide","dotDivide","dotMultiply","dotPow","exp","expm1","fix","floor","gcd","hypot","lcm","log","log10","log1p","log2","mod","multiply","norm","nthRoot","nthRoots","pow","round","sign","sqrt","square","subtract","unaryMinus","unaryPlus","xgcd","bitAnd","bitNot","bitOr","bitXor","leftShift","rightArithShift","rightLogShift","bellNumbers","catalan","composition","stirlingS2","arg","conj","im","re","distance","intersect","and","not","or","xor","apply","column","concat","count","cross","ctranspose","det","diag","diff","dot","eigs","expm","filter","flatten","forEach","getMatrixDataType","identity","inv","kron","map","matrixFromColumns","matrixFromFunction","matrixFromRows","ones","partitionSelect","range","reshape","resize","rotate","rotationMatrix","row","size","sort","sqrtm","squeeze","subset","trace","transpose","zeros","combinations","combinationsWithRep","factorial","gamma","kldivergence","multinomial","permutations","pickRandom","random","randomInt","compare","compareNatural","compareText","deepEqual","equal","equalText","larger","largerEq","smaller","smallerEq","unequal","setCartesian","setDifference","setDistinct","setIntersect","setIsSubset","setMultiplicity","setPowerset","setSize","setSymDifference","setUnion","erf","mad","max","mean","median","min","mode","prod","quantileSeq","std","sum","variance","bin","format","hex","oct","print","acos","acosh","acot","acoth","acsc","acsch","asec","asech","asin","asinh","atan","atan2","atanh","cos","cosh","cot","coth","csc","csch","sec","sech","sin","sinh","tan","tanh","to","clone","hasNumericValue","isInteger","isNaN","isNegative","isNumeric","isPositive","isPrime","isZero","numeric","typeOf"]

process.argv.forEach(function (arg) {
    if (arg.includes("manConfig=")) {
        customConfig = arg.split("=")[1]
        customConfig = JSON.parse(fs.readFileSync(__dirname+'/customConfigs/'+customConfig))
        if (customConfig.modbusWriteEnabled != undefined) {
            modbusWriteEnabled = customConfig.modbusWriteEnabled
        }
        if (customConfig.modbusOptions != undefined) {
            externalConfig.modbusOptions = customConfig.modbusOptions
        }
        if (customConfig.queryInterval != undefined) {
            externalConfig.queryInterval = customConfig.queryInterval
        }
    }
});

async function modbusWrite(edata, qdata, globalVars) {
    try {
        if (modbusWriteEnabled) {
            const opcConfig = externalConfig.modbusOptions

            modbusClient = new ModbusTcpClient(
                opcConfig.modbusConnection.host, 
                opcConfig.modbusConnection.port, 
                {
                    autoReconnect:          opcConfig.modbusConnection.autoReconnect,
                    autoReconnectInterval:  opcConfig.modbusConnection.autoReconnectInterval,
                    debug:                  opcConfig.modbusConnection.debug
                }
            );
    
            modbusClient.on('error', err => {
                console.error('modbus error', err);
            });
    
            modbusClient.on('disconnect', () => {
                console.warn('modbus disconnect');
            });
    
            modbusClient.on('reconnect', () => {
                console.info('modbus reconnect');
            });
    
            await modbusClient.connect()
            if(modbusClient.isConnected()){
                console.info('modbus connect')

                var variablesToWrite = [];
                for (var key in opcConfig.tags) {
                    data =  undefined;
                    if (edata[key]!= undefined) {
                        data = edata[key]
                    } else if (qdata[key]!= undefined) {
                        data = qdata[key]
                    }
    
                    if (data != undefined) {
                        let val = data;
                        if (typeof data == "object") {
                            let i = data.length-1;
                            val = data[i];
    
                            while ((i>0) && (isNaN(data[i]))) {
                                i -= 1
                                val = data[i];
                            }
                        }
    
                        if (!isNaN(val)) {
                            variablesToWrite.push({
                                key: key,
                                val: val
                            })
    
                        }
                    } else {
                        console.log(key+" is not in calculation results")
                    }
                }

                if (variablesToWrite.length > 0) {
                    for(const tag of variablesToWrite){
                        var address = opcConfig.tags[tag.key]
                        var data = buildModbusArrayToWrite(tag.val) 
                        await modbusClient.writeMultipleRegisters(address, data)
                        if (!globalVars.isLambda) {
                            globalVars.logMessage = globalVars.logMessage+"\n"+"writing "+tag.key+" = "+tag.val
                        }
                        console.log("writing", tag.key, " = ", tag.val)
                        // check if written correctly
                        var readData = await modbusClient.readHoldingRegisters(address, 2)
                        var readVal = uint16ToFloat32(readData.result[1], readData.result[0])
                        console.log("reading", tag.key, " = ", readVal)
                    }
                }
    
                await modbusClient.disconnect();
                
                if (!globalVars.isLambda) {
                    globalVars.logMessage = globalVars.logMessage+"\n"+"exiting modbus write function!"
                }
                console.log("exiting modbus write function!");
            }
        }
    } catch (e) {
        if (!globalVars.isLambda) {
            globalVars.ErrorMessage = globalVars.ErrorMessage+"\n"+"modbus write failed, error message = " + e
        }
        console.log("modbus write failed, error message = " + e)
    }
}

uint16ToFloat32 = (low, high) => {
    var buffer = new ArrayBuffer(4);
    var intView = new Uint16Array(buffer);
    var floatView = new Float32Array(buffer);

    intView[0] = high;
    intView[1] = low;
    return floatView[0];
}

//Function to create an array of values to write sequentially
buildModbusArrayToWrite = (value) =>{
    modbusArray = [];
    modbusArray = float32ToUint16(checkValidFloat32(value));
    return modbusArray;
}

float32ToUint16 = (value) => {
    var buffer = new ArrayBuffer(4);
    var intView = new Uint16Array(buffer);
    var floatView = new Float32Array(buffer);

    floatView[0] = value;
    return [intView[0], intView[1]];
}

//Function to ensure values are numbers, then checks if it's a valid float32 number. Return -1 if not, as per BHP modbus standard
checkValidFloat32 = (testNumber) =>{
    if (Number.isNaN(testNumber) === false){
        if ((testNumber >= -340000000000000000000000000000000000) && (testNumber <= 340000000000000000000000000000000000)){
            return testNumber;
        } else {
            return -1;
        }
    } else{
        return -1;
    }
}

async function opcUAWrite (edata, qdata, globalVars) {
    try {                   
        var data =  undefined;
        if (opcUAWriteEnabled) {
            const opcConfig = externalConfig.opcUAOptions
            const opcUAClient = opcUA.OPCUAClient.create({
                applicationName: "opcUAClient",
                connectionStrategy: {
                    initialDelay: 1000,
                    maxRetry: 1
                },
                securityMode: opcUA.MessageSecurityMode.None,
                securityPolicy: opcUA.SecurityPolicy.None,
                endpointMustExist: false,
                // certificateFile: "./client_certificate.pem",
                // privateKeyFile: "./private_key.pem"
            });
            await opcUAClient.connect(opcConfig.endpointUrl);
            
            if (!globalVars.isLambda) {
                globalVars.logMessage= globalVars.logMessage+"\n"+"connected to OPC UA endpoint!"
            }
            console.log("connected to OPC UA endpoint!");
            const session = await opcUAClient.createSession();
            //const session = await opcUAClient.createSession({ userName: "PIQcalcServer", password: "UAm371xsnUCiVsGc" });
            console.log("opc UA session created !");
            if (!globalVars.isLambda) {
                globalVars.logMessage= globalVars.logMessage+"\n"+"opc UA session created !"
            }

            var nodesToWrite = [];
            for (var key in opcConfig.addressMap) {
                 data =  undefined;
                if (edata[key]!= undefined) {
                    data = edata[key]
                } else if (qdata[key]!= undefined) {
                    data = qdata[key]
                }

                if (data != undefined) {
                    let val = data;
                    if (typeof data == "object") {
                        let i = data.length-1;
                        val = data[i];

                        while ((i>0) && (isNaN(data[i]))) {
                            i -= 1
                            val = data[i];
                        }
                    }

                    if (key == "influxTimeHeartBeat") {
                        val = Math.round((moment().format('x')/(60*1000))%100)
                    }

                    if (!isNaN(val)) {
                        nodesToWrite.push({
                            nodeId: opcConfig.addressMap[key],
                            attributeId: opcUA.AttributeIds.Value,
                            value:{
                                value: {
                                    dataType: opcUA.DataType.Float,
                                    value: val
                                }
                            }
                        })
                        
                        if (!globalVars.isLambda) {
                            globalVars.logMessage= globalVars.logMessage+"\n"+key+" = "+val
                        }
                        console.log(key+" = "+val)
                    }
                } else {
                    if (!globalVars.isLambda) {
                        globalVars.WarningMessage= globalVars.WarningMessage+"\n"+key+" is not in calculation results"
                    }
                    console.log(key+" is not in calculation results")
                }
            }

            if (nodesToWrite.length > 0) {
                await session.write(nodesToWrite, function (err, statusCodes) {
                    if (err) {
                        if (!globalVars.isLambda) {
                            globalVars.ErrorMessage= globalVars.ErrorMessage+"\n"+"opc UA write error = "+err
                        }
                        
                        console.log("opc UA write error = "+err);
                    } else {
                        if (!globalVars.isLambda) {
                            globalVars.logMessage= globalVars.logMessage+"\n"+"opc write successfull!"
                        }
                        console.log("opc write successfull!")
                    }
                })
            }

            await session.close();
            await opcUAClient.disconnect();
            
            if (!globalVars.isLambda) {
                globalVars.logMessage= globalVars.logMessage+"\n"+"exiting opc write function!"
            }
            console.log("exiting opc write function!");
        }
    } catch (e) {
        if (!globalVars.isLambda) {
            globalVars.ErrorMessage= globalVars.ErrorMessage+"\n"+"OPC UA write failed, Error message = "+e
        }
        console.log("OPC UA write failed, Error message = "+e)
    }
}

async function opcUARead (globalVars) {
    let res = {}
    try {
        const opcConfig = externalConfig.opcUAOptions
        const opcUAClient = opcUA.OPCUAClient.create({
            applicationName: "opcUAClient",
            connectionStrategy: {
                initialDelay: 1000,
                maxRetry: 1
            },
            securityMode: opcUA.MessageSecurityMode.None,
            securityPolicy: opcUA.SecurityPolicy.None,
            endpointMustExist: false,
            // certificateFile: "./client_certificate.pem",
            // privateKeyFile: "./private_key.pem"
        });
        await opcUAClient.connect(opcConfig.readEndpointUrl);
        if (!globalVars.isLambda) {
                globalVars.logMessage= globalVars.logMessage+"\n"+"connected to OPC UA endpoint!"
            }
        console.log("connected to OPC UA endpoint!");
        const session = await opcUAClient.createSession();
        //const session = await opcUAClient.createSession({ userName: "PIQcalcServer", password: "UAm371xsnUCiVsGc" });
        if (!globalVars.isLambda) {
                globalVars.logMessage= globalVars.logMessage+"\n"+"opc UA session created !"
            }
        console.log("opc UA session created !");

        for (var key in opcConfig.readAddressMap) {
            res[key] = await session.read({
                nodeId: opcConfig.readAddressMap[key],
                attributeId: opcUA.AttributeIds.Value
            }, 0)

            res[key] = res[key].value.value
        }

        await session.close();
        await opcUAClient.disconnect();
        if (!globalVars.isLambda) {
                globalVars.logMessage= globalVars.logMessage+"\n"+"exiting opc read function!"
            }
        console.log("exiting opc read function!");
    } catch (e) {
        if (!globalVars.isLambda) {
                globalVars.ErrorMessage= globalVars.ErrorMessage+"\n"+"OPC UA read failed, Error message = "+e
            }
        console.log("OPC UA read failed, Error message = "+e)
    }

    return res
}

function logRecorder (m, globalVars) {
    globalVars["serverLogToReturn"] += "\n"+m
    console.log(m)
}

async function connectTOMysqlServer (serverconfig, globalVars) {
    var connection = await mysql.createConnection(serverconfig)

    connection.connect();

    connection.on("error", function (err) {
        console.log("error from mysql id (" + serverconfig.user + ") error=", err);
    });

    globalVars["Servers"].set(serverconfig, connection);
}

function connectAllServers (globalVars, isLambda) {
    globalVars["Servers"] = new Map();
    globalVars["writeServers"] = new Map();
    let site = globalVars["RTcalcSetup"].Site

    for (let connectionName of config.orgs[site]) {
        connectServer(config.servers[connectionName], globalVars, isLambda)
    }
}

function disconnectAllServers (globalVars) {
    let site = globalVars["RTcalcSetup"].Site

    for (let connectionName of config.orgs[site]) {
        disconnectServer(config.servers[connectionName], globalVars)
    }
}


const connectTOMqttServer = async (serverconfig, globalVars) => {
    var MQTToptions = {
        keepalive: serverconfig.keepalive,
        clientId: serverconfig.user,
        protocolId: 'MQTT',
        protocolVersion: 4,
        clean: true,
        reconnectPeriod: serverconfig.reconnectPeriod,
        connectTimeout: serverconfig.connectTimeout,
        will: {
            topic: 'WillMsg',
            payload: 'Connection Closed abnormally..!',
            qos: 0,
            retain: false
        },
        username: serverconfig.user,
        password: serverconfig.password,
        rejectUnauthorized: false,
    };
    var mqttClient = await mqtt.connect(serverconfig.host, MQTToptions);
    /*** MQTT.js mqttClient on event connect ********************************************************************************************/
    mqttClient.on("connect", function (connack) {
        console.log("mqttClient id (" + serverconfig.user + ") is CONNECTED to IoT_DAserver");
        if (connack.sessionPresent == false) {
            // subscribe to a topic. subscribe method takes two arguments {topic,options}.
            var subscribeTopicStr = serverconfig.user;
            mqttClient.subscribe(subscribeTopicStr, { qos: 1 }, function (err, granted) {
                if (err)
                    console.log(err);
                else
                    console.log("IOT Device mqttClient id (" + serverconfig.user + ") subscribe to: " + subscribeTopicStr, granted);
            });
        }
    });
    /*** MQTT.js mqttClient on event reconnect ***/
    mqttClient.on("reconnect", function () {
        console.log("mqttClient id (" + serverconfig.user + ") is reconnecting");
    });
    /*** MQTT.js mqttClient on event error ***/
    mqttClient.on("error", function (err) {
        console.log("error from mqttClient id (" + serverconfig.user + ") error=", err);
    });
    /*** MQTT.js mqttClient on event close ***/
    mqttClient.on("close", function () {
        console.log("mqttClient id (" + serverconfig.user + ") is CLOSED");
    });
    /*** MQTT.js mqttClient on event offline ***/
    mqttClient.on("offline", function (err) {
        console.log("mqttClient id (" + serverconfig.user + ") is OFFLINE");
    });
    /*** MQTT.js mqttClient listen for a message from MQTT server to a subscribed topic ***/
    mqttClient.on("message", function (topic, message, packet) {
        console.log("Control signal sent from MillRoC => " + topic);
        console.log("Control signals: ");
        console.log(message.toString());
        console.log("Control signals: ");
        console.log(JSON.parse(message.toString()));
        console.log("Control signals packet: " + packet);
    });
    mqttClient.on("packetreceive", function (packet) {
        console.log("packetreceive event: ");
        console.log(packet);
    });
    globalVars["Servers"].set(serverconfig, mqttClient);
}


async function connectServer (serverstr, globalVars, isLambda) {
    try {
        if (serverstr != undefined) {
            if (serverstr.type == "influx") {
                await globalVars["Servers"].set(serverstr, new influx.InfluxDB(serverstr.host + ':' + serverstr.readPort + '/' +serverstr.database));
                if (serverstr.writeHost != undefined && !isLambda) {
                    await globalVars["writeServers"].set(serverstr, new influx.InfluxDB(serverstr.writeHost + ':' + serverstr.writePort + '/' +serverstr.database));
                } else {
                    await globalVars["writeServers"].set(serverstr, new influx.InfluxDB(serverstr.host + ':' + serverstr.writePort + '/' +serverstr.database));
                }
            } else if (serverstr.type == "mqtt") {
                // connectTOMqttServer(serverstr, globalVars);
            } else if (serverstr.type == "mysql") {
                connectTOMysqlServer(serverstr, globalVars);
            }
        }
    }
    catch (err) {
        if (!globalVars.isLambda) {
            globalVars.ErrorMessage = globalVars.ErrorMessage+"\n"+"ERR in connectServer() = " +err
        }
        logRecorder("ERR in connectServer() = " +err, globalVars);
    }
};

const disconnectServer = async (serverstr, globalVars) => {
    try {
        if (serverstr != undefined) {
            if (serverstr.type == "influx") {
                //"The underlying connections to influx are based on HTTP requests, which means that there are no long lived connections being kept alive. So it is not necessary to close an connection."
                // await Servers.set(serverstr, new influx.InfluxDB(serverstr.host + ':' + serverstr.port + '/' +serverstr.database));
            } else if (serverstr.type == "mqtt") {
                // add disconnect for this
            } else if (serverstr.type == "mysql") {
                (globalVars["Servers"].get(serverstr)).end();
            }
        }
    }
    catch (err) {
        if (!globalVars.isLambda) {
            globalVars.ErrorMessage = globalVars.ErrorMessage+"\n"+"ERR in disconnectServer() = " +err
        }
        logRecorder("ERR in disconnectServer() = " +err, globalVars);
    }
};

const makeQuery = async (querySrv, querystr, type, globalVars) => {
    var datar;
    try {
        if (type == "influx") {
            await querySrv.query(querystr).then(result => {
                datar = result;
            }).catch(err => {
                if (!globalVars.isLambda) {
                    globalVars.ErrorMessage = globalVars.ErrorMessage+"\n"+"Err making influx query for "+querystr+" error = "+err
                }
                logRecorder("Err making influx query for "+querystr+" error = "+err, globalVars)
            });
        } else if (type == "mysql") {
            await mysqlQuery(querySrv, querystr, globalVars).then(result => {
                datar = result;

            }).catch(err => {
                if (!globalVars.isLambda) {
                    globalVars.ErrorMessage = globalVars.ErrorMessage+"\n"+"Err making mysql query for "+querystr+" error = "+err
                }
                logRecorder("Err making mysql query for "+querystr+" error = "+err, globalVars)
            });
        };

   } catch (e) {
       // console.log(e);
   }

   return datar;
}


function resetCumSumQueryStr (querystr) {
    let replaceArr = querystr.match(/\[\[(.*?)\]\]/g);
    if (replaceArr != null) {
        for (let replaceStr of replaceArr) {
            let newStr = replaceStr
            newStr = newStr.replace(/\[\[/g, " ");
            newStr = newStr.replace(/\]\]/g, " ");
            querystr = querystr.replace(replaceStr, newStr)
        }
    }
    
    return querystr
}

function parseInfluxTimeExpression(expression) {
    if (isNaN(expression)) {
        const now = Date.now();
        if (expression === "now") return JSON.stringify(Math.floor(now / 1000))+'s';
        let relativeMatch = expression.match(/now\s*([-+])\s*(\d+)([smhdw])/);
        
        if (relativeMatch) {
            const operator = relativeMatch[1];
            const value = parseInt(relativeMatch[2], 10);
            const unit = relativeMatch[3];
            const unitMultipliers = { s: 1000, m: 60 * 1000, h: 60 * 60 * 1000, d: 24 * 60 * 60 * 1000, w: 7 * 24 * 60 * 60 * 1000 };
            const offset = value * unitMultipliers[unit];
            const result = operator === "-" ? now - offset : now + offset;
            return JSON.stringify(Math.floor(result / 1000))+'s';
        }
        const timestampMatch = Date.parse(expression);
        if (!isNaN(timestampMatch)) return JSON.stringify(Math.floor(timestampMatch / 1000))+'s';
    }
    return expression
}

function convertToUnixTime(input) {
    input = String(input).trim();
    input = input.replace(/'/g, "")

    if (typeof input === "number" || /^\d+(s|ns)?$/.test(input)) {
        input = input.replace(/ns$/, '').replace(/s$/, '');
        if (input.length === 10) return parseInt(input, 10); 
        if (input.length === 13) return Math.floor(parseInt(input, 10) / 1000); 
        if (input.length === 16) return Math.floor(parseInt(input, 10) / 1000000);
        if (input.length === 19) return Math.floor(parseInt(input, 10) / 1000000000);
    } else if (typeof input === "string") {
        const isoTime = moment.utc(input, moment.ISO_8601, true); 
        if (isoTime.isValid()) return isoTime.unix(); 
        const customTime = moment.utc(input, ["YYYY-MM-DD HH:mm:ss", "YYYY-MM-DD"], true); 
        if (customTime.isValid()) return customTime.unix(); 
    }
}

const queryDatabase = async (queries, defaultDB, customInterval, timeFrom, timeTo, periodForContinious, isWriteBack, globalVars) => {

    let querySrv;
    let querystr = '';
    let queryresult = {}
    queryresult["map"] = {influx: new Map(), mysql:  new Map()}
    queryresult["obj"] = {}
    let timeNow = moment().utc().subtract(externalConfig.queryInterval, "seconds").unix().toString()+"s"
    // if (periodForContinious) {
    //     timeNow = moment().utc().subtract(periodForContinious, "seconds").unix().toString()+"s"
    // }

    globalVars["globalScopeLastValues"] = {};
    for (let [k, v] of queries) { 
        try {
            for (key in config.servers) {
                let dbstr = k.split(';');
                if (key == v[0].readFrom) {
                    if (config.servers[key].type == "influx") {
                        let qWithNoData = [];
                        querySrv = globalVars["Servers"].get(config.servers[key]);
                        querystr = '';

                        let qInterval = externalConfig.avgInterval;
                        // if (customInterval != undefined) {
                        //     qInterval = customInterval;
                        // }

                        if (dbstr.length > 2) {
                            let timeRange = " time >= "+timeNow+" "

                            if (timeFrom && timeTo) {
                                timeRange = 'time <= '+timeTo +' and time >= '+timeFrom
                            } 

                            for (var i = 0; i < v.length; i++) {
                                querystr = v[i].queryString.replace(/ from /ig, " from ");
                                querystr = querystr.replace(' from ', " as "+v[i].variable+" from ");
                                querystr = querystr.replace("$timeFilter", timeRange);
                                querystr = querystr.replace(/^\s*\\\"/, " ");
                                querystr = querystr.replace(/\\\"\s*$/, " ");
                                querystr = querystr.replace(/^\s*\"/, " ");
                                querystr = querystr.replace(/\"\s*$/, " ");

                                let qRes

                                if (querystr.match(/\[\[(.*?)\]\]/g) != null) {
                                    var sumStartTime = convertToUnixTime(resetCumSumQueryStr(querystr.match(/\[\[(.*?)\]\]/g)[0]))
                                    if (dbstr[2] != globalVars["RTcalcSetup"].Site) {
                                        let lastQueryStr = 'SELECT last("'+v[i].variable+'") AS lastValue  FROM ' + config.servers[dbstr[2]].table
                                        let lastValQ = await makeQuery(globalVars["Servers"].get(config.servers[dbstr[2]]), lastQueryStr, "influx", globalVars)
                                        let lastVal = false
                                        let lastTime = false

                                        if (lastValQ[0] != undefined) {
                                            lastVal = lastValQ[0].lastValue
                                            lastTime = moment(lastValQ[0].time).unix()
                                        }
                                        
                                        let fromInt = timeFrom? 
                                            JSON.parse(timeFrom.split("s")[0])
                                            :JSON.parse(timeNow.split("s")[0])
                                        let toInt = timeTo?
                                            JSON.parse(timeTo.split("s")[0])
                                            :false

                                        if (lastVal === false || lastTime < sumStartTime) {
                                            querystr = resetCumSumQueryStr (querystr)
                                            qRes = await makeQuery(querySrv, querystr, "influx", globalVars)
                                        } else {
                                            let isSkipSum = false
                                            if (lastTime < fromInt) {
                                                querystr = querystr.replace(/\[\[(.*?)\]\]/g, lastTime+"s")
                                            } else if (fromInt < sumStartTime) {
                                                querystr = resetCumSumQueryStr (querystr)
                                                isSkipSum = true
                                            } else {
                                                lastQueryStr = 'SELECT last("'+v[i].variable+'") AS lastValue FROM ' + config.servers[dbstr[2]].table + ' where time<='+fromInt+'s and time>='+sumStartTime+'s'
                                                lastValQ = await makeQuery(globalVars["Servers"].get(config.servers[dbstr[2]]), lastQueryStr, "influx", globalVars)
                                                if (lastValQ[0] != undefined) {
                                                    lastVal = lastValQ[0].lastValue
                                                    lastTime = moment(lastValQ[0].time).unix()
                                                    querystr = querystr.replace(/\[\[(.*?)\]\]/g, lastTime+"s")
                                                } else {
                                                    querystr = resetCumSumQueryStr (querystr)
                                                    isSkipSum = true
                                                }
                                            }

                                            qRes = await makeQuery(querySrv, querystr, "influx", globalVars)

                                            if (!isSkipSum) {
                                                let add = lastVal
                                                if (lastTime == moment(qRes[0].time).unix()) {
                                                    add -= qRes[0][v[i].variable]
                                                }

                                                for (let arr of qRes) {
                                                    if (lastTime != moment(arr.time).unix()) {
                                                        arr[v[i].variable] += add
                                                    } else {
                                                        arr[v[i].variable] = lastVal
                                                    }
                                                }
                                            }
                                        }
                                    } else {
                                        querystr = resetCumSumQueryStr (querystr)
                                        qRes = await makeQuery(querySrv, querystr, "influx", globalVars)
                                    }
                                } else {
                                    qRes = await makeQuery(querySrv, querystr, "influx", globalVars)
                                }

                                let isLastQ = false;

                                if (qRes != undefined) {
                                    if (qRes[0] != undefined) {
                                        if (Object.keys(qRes[0]).length > 1) {
                                            queryresult["map"]["influx"].set(k, qRes);
                                            queryresult["obj"][k] = qRes;
                                        } else {
                                            isLastQ = true;
                                        }
                                    } else {
                                        isLastQ = true;
                                    }
                                } else {
                                    isLastQ = true;
                                }

                                if (isLastQ) {
                                    if (querystr.toLowerCase().includes("fill") && querystr.toLowerCase().includes("(previous)")) {
                                        qWithNoData.push(v[i].variable)
                                    } else {
                                        queryresult["map"]["influx"].set(k, undefined);
                                        queryresult["obj"][k] = [];
                                    }
                                } 
                            }

                            if (queryresult["map"]["influx"].get(k) != undefined) {
                                let temp = JSON.parse(JSON.stringify(queryresult["map"]["influx"].get(k)))     
                                if (temp.length > 0) {
                                    let newArray = [];
                                    varKey = (Object.keys(temp[0])[1])
                                    for (let d of temp) {
                                        if (isFinite(d[varKey]) && !isNaN(d[varKey]) && d[varKey]!=null) {
                                            let temp = {Timestamp2:moment(d.time).format('x')*1000000}
                                            temp[varKey] = d[varKey];
                                            newArray.push(temp)
                                        }
                                    }   
                                    if (isWriteBack) {
                                        writeInfluxHelper(dbstr[2], JSON.stringify(newArray), globalVars)
                                    }
                                }
                            }

                        } else if (dbstr.length <= 2) {
                            let varList = [];
                            for (var i = 0; i < v.length; i++) {
                                querystr = querystr + "mean(" + v[i].tagname + ") AS " + v[i].variable + ","
                                if (!varList.includes(v[i])) {
                                    varList.push(v[i].variable)
                                }
                            }

                            let timeRange = 'time >= '+timeNow+' '

                            if (timeFrom && timeTo) {
                                timeRange = 'time <= '+timeTo +' and time >= '+timeFrom
                            } 

                            querystr = 'SELECT ' + querystr + ' mean("Time stamp2") AS timestamp  FROM ' + dbstr[1] + ' WHERE '+timeRange+' GROUP BY time(' + qInterval + ') fill(previous) ';
                            
                            // querystr = 'SELECT ' + querystr + ' mean("Time stamp2") AS timestamp  FROM ' + dbstr[1] + ' WHERE time >= \'2020-01-22T00:00:00Z\' AND time <= \'28-01-28T09:00:00Z\' ' + 'GROUP BY time(' + qInterval + 'm) fill(previous) tz(\'Australia/Perth\')'; // DEBUG use
                            let qRes = await makeQuery(querySrv, querystr, "influx", globalVars)
                            queryresult["map"]["influx"].set(k, qRes);
                            queryresult["obj"][k] = qRes

                            if (qRes != undefined) {
                                if (qRes[0] != undefined) {
                                    for (let tagK of Object.keys(qRes[0])) {
                                        if (tagK != "time" && tagK != "timestamp") {
                                            if (qRes[0][tagK] == null) {
                                               let isLastQ = true;
                                               for (let val of qRes) {
                                                   if (val[tagK] != null) {
                                                       isLastQ = false;
                                                   }
                                               }

                                               if (isLastQ) {
                                                   qWithNoData.push(tagK)
                                               }
                                            } 
                                        }
                                    }
                                } else {
                                    qWithNoData.concat(varList)
                                }
                            } else {
                                qWithNoData.concat(varList)
                            }
                        }

                        if (qWithNoData.length > 0 && config.servers[defaultDB] != undefined) {
                            for (let tag of qWithNoData) {
                                let timeRange = 'time < '+timeNow+' '

                                if (timeFrom && timeTo) {
                                    timeRange = 'time < '+timeFrom
                                } 

                                let  querystr = 'SELECT  last('+tag+') from '+config.servers[defaultDB].table+' where '+timeRange;
                                let qRes = await makeQuery(querySrv, querystr, "influx", globalVars);

                                if (qRes != undefined) {
                                    if (qRes[0] != undefined) {
                                        if (qRes[0].last != undefined) {
                                            if (isFinite(qRes[0].last) && !isNaN(qRes[0].last)) {
                                                globalVars["globalScopeLastValues"][tag] =  qRes[0].last;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        
                    } else if (config.servers[key].type == "mysql") {
                        querystr = '';
                        for (var i = 0; i < v.length; i++) {
                            if (i == 0) {
                                querystr = "\`"+v[i].tagname.split("\"")[1]+"\` AS "+v[i].variable;
                            } else {
                                querystr = querystr+", \`"+v[i].tagname.split("\"")[1]+"\` AS "+v[i].variable;
                            }
                        }

                        querystr = 'SELECT '+querystr+' FROM \`'+dbstr[1].split("\"")[1]+'\` ORDER BY \`'+dbstr[1].split("\"")[1]+'_Id\` DESC LIMIT 1';
                        querySrv = globalVars["Servers"].get(config.servers[key]);
                        let tempSql = await makeQuery(querySrv, querystr, "mysql", globalVars);
                        queryresult["map"]["mysql"].set(k, tempSql);
                        queryresult["obj"][k] = tempSql;
                    }
                }
            }
        } catch (err) {
            if (!globalVars.isLambda) {
                    globalVars.ErrorMessage = globalVars.ErrorMessage+"\n"+"ERR in queryDatabase() var query= " + err
                }
            logRecorder("ERR in queryDatabase() var query= " + err, globalVars);
        }
    }

    queryresult["obj"] = transposeForSendBack (queryresult["obj"]) 
    return queryresult;
}

function transposeForSendBack(obj) {
    let result = {}
    for (let type in obj) {
        for (let key in obj[type]) {
            for (let k in obj[type][key]) {
                if (result[k] != undefined) {
                    result[k].push(obj[type][key][k])
                } else {
                    result[k] = [obj[type][key][k]]
                }
            }
        }
    }

    return result
}

function mysqlQuery(querySrv, querystr, globalVars) {

    return new Promise((resolve,reject) => {
            querySrv.query(querystr, function (err, res, fields) {
               if (err) {
                   makeQuery(querySrv, querystr, 'mysql', globalVars);
               } else {
                   resolve(JSON.parse(JSON.stringify(res[0])));
               }
            });

    });
}

function setMysqlConstants(MysqlMap, globalVars) {
    for (const [k, d] of MysqlMap.entries()) {
        for (let key in d) {
            var tmpn = Number(d[key]);
            if (isFinite(tmpn) && !isNaN(tmpn)) {
                globalVars["globalScopedata"].set(key, tmpn);
            } else {
                logRecorder("error: Mysql constant is infinite/Nan", globalVars);
            }
        }
    }
}

function processQueryResult(qresults, tagInfo, globalVars) {
    if (qresults["mysql"] != undefined) {
        setMysqlConstants(qresults["mysql"], globalVars)
    }

    let infinityArray = []
    try {
        let keys = Array.from(qresults["influx"].keys());
        let keyWithTime = "";
        for (let key of keys) {
            if (key.split(";").length == 2) {
                let o = qresults["influx"].get(key);
                if (o[0] != undefined) {
                    let obj = o[0];
                    if (obj.time != undefined && keyWithTime == "") {
                        keyWithTime = key;
                    }
                }
            }
        }

        if (keyWithTime != "") {
            let vv = qresults["influx"].get(keyWithTime);
            let timeMinutesRoundedDown = {};
            for (var i = 0; i < vv.length; i++) {
                let dt = moment(vv[i].time._nanoISO).format('x');
                globalVars["globalTimestamp"].push(dt);
                timeMinutesRoundedDown[Math.floor(dt.valueOf()/60000)] = dt;
                infinityArray.push("Infinity");
            }
            for (const [k, d] of qresults["influx"].entries()) {
                let expectedStampKey = 0;
                let prevIterMinTime = 0;
                if (d != undefined && d.length > 1) {
                    for (var i = 0; i < d.length; i++) {
                        let currentElement = d[i];
                        let millis = moment(currentElement.time._nanoISO).format('x');
                        let timeMinutes = Math.floor(millis.valueOf()/60000);
                        for (key in currentElement) {
                            if (!globalVars["shiftedKeys"].includes(key)) {
                                if (globalVars["globalTimestamp"].includes(millis.valueOf()) || timeMinutesRoundedDown[timeMinutes] != undefined) {
                                    if (key != 'timestamp' && key != 'time') {
                                        let val = currentElement[key];
                                        if (currentElement[key] == null) {
                                            val = "Infinity";
                                        }

                                        if (globalVars["globalScopedata"].has(key)) {
                                            let da = globalVars["globalScopedata"].get(key);

                                            if ((k.split(";").length == 2) || (k.split(";").length > 2 && (prevIterMinTime != timeMinutes || prevIterMinTime == 0))) {
                                                da.push(val);
                                            } else if ((k.split(";").length > 2 && (prevIterMinTime == timeMinutes || prevIterMinTime == 0))) {
                                                da[da.length-1] = val;
                                            }
                                        } else {
                                            let da2 = new Array();
                                            da2.push(val);
                                            globalVars["globalScopedata"].set(key, da2);
                                        }
                                    }
                                    
                                    expectedStampKey++;
                                } else if (timeMinutesRoundedDown[Object.keys(timeMinutesRoundedDown)[expectedStampKey]] < millis) {
                                    if (key != 'timestamp' && key != 'time') {
                                        if (globalVars["globalScopedata"].has(key)) {
                                            let da = globalVars["globalScopedata"].get(key);
                                            da.push("Infinity");
                                        } else {
                                            let da2 = new Array();
                                            da2.push("Infinity");
                                            globalVars["globalScopedata"].set(key, da2);
                                        }

                                        expectedStampKey++;
                                    }
                                }
                            }
                        }

                        for (key of globalVars["shiftedKeys"]) {
                            if (currentElement[key] != undefined) {
                                
                                if (key != 'timestamp' && key != 'time') {
                                    let val = currentElement[key];
                                    if (currentElement[key] == null) {
                                        val = "Infinity";
                                    }

                                    if (globalVars["globalScopedata"].has(key)) {
                                        let da = globalVars["globalScopedata"].get(key);
                                        da.push(val);
                                    } else {
                                        let da2 = new Array();
                                        da2.push(val);
                                        globalVars["globalScopedata"].set(key, da2);
                                    }
                                }
                            }
                        }

                        prevIterMinTime = timeMinutes;
                    }

                    for (key of globalVars["shiftedKeys"]) {
                        if (globalVars["globalScopedata"].has(key)) {
                            let da = globalVars["globalScopedata"].get(key);
                            if (da.length > infinityArray.length) {
                                da = da.slice(0, infinityArray.length)
                                globalVars["globalScopedata"].set(key, da)
                            } else if (da.length < infinityArray.length) {
                                let diff = infinityArray.length-da.length
                                for (let i=0; i<diff; i++) {
                                    da.push("Infinity")
                                }
                            }
                        }
                    }
                } else if (d != undefined && d.length == 1) { // Letiing through "Select last() and other queries that return single value results" queries
                    for (key in d[0]) {
                        if (key != 'timestamp' && key != 'time') {
                            let val = d[0][key]
                            if (d[0][key] == null) {
                                val = "Infinity";
                            }

                            let da2 = new Array();
                            da2.push(val);
                            globalVars["globalScopedata"].set(key, da2);
                        }
                    }
                }
            }

            for (let [k, d] of tagInfo.entries()) {
                for (let info of d) {
                    if (!globalVars["globalScopedata"].has(info.variable)) {
                        let da = infinityArray;
                        globalVars["globalScopedata"].set(info.variable, da);
                    } else if  (!((globalVars["globalScopedata"].get(info.variable)).length > 0)) {
                        let da = infinityArray;
                        globalVars["globalScopedata"].set(info.variable, da);
                    }
                }
            }
        } else {
            if (!globalVars.isLambda) {
                globalVars.WarningMessage = globalVars.WarningMessage+"\n"+"ERR: Timestamps are missing in all the query results"
            }
            logRecorder("ERR: Timestamps are missing in all the query results", globalVars)
        }

        for (let tag in globalVars["globalScopeLastValues"]) {
            let lasTagtData = []; 
            
            for (let i=0; i<infinityArray.length; i++) {
                lasTagtData.push(globalVars["globalScopeLastValues"][tag])
            }

            globalVars["globalScopedata"].set(tag, lasTagtData);
        }
    } catch (err) {
        if (!globalVars.isLambda) {
                globalVars.ErrorMessage = globalVars.ErrorMessage+"\n"+"ERR in processQueryResult() = " + err
            }
        logRecorder("ERR in processQueryResult() = " + err, globalVars);
    }
}

function filterScopeVar(conststr, filterv, eqName, globalVars) {
    let fa = [];
    let ta = [];

    for (var i = 0; i < conststr.length; i++) {
        if (filterv[i] || (filterv == false)) {
            fa.push(conststr[i]);
            ta.push(globalVars["globalTimestamp"][i]);
        }
    }

    globalVars["globalEquationTime"].set(eqName, ta);

    return fa;
}

function convertTreeToString(tree) {
    var temp = [];
    var i = 0;
    for (var key in tree) {
        branch = tree[key]
        temp[i] = branch.toString();
        i = i + 1;
    }

    return temp
}

function getTagFilters(globalVars) {
    globalVars["tagFilters"] = {};

    if (globalVars["RTcalcSetup"].Equation.length > 0) {
        for (var i = 0; i < globalVars["RTcalcSetup"].Equation.length; i++) {
            if (globalVars["RTcalcSetup"].Equation[i].equation != "FinalTagFilter") {
                if (globalVars["RTcalcSetup"].Equation[i].equation.endsWith("FFilter")) {
                    let temp = globalVars["RTcalcSetup"].Equation[i].equation;
                    globalVars["tagFilters"][temp] = globalVars["RTcalcSetup"].Equation[i].equationString;
                }
                globalVars["globalEquations"].set(globalVars["RTcalcSetup"].Equation[i].equation, globalVars["RTcalcSetup"].Equation[i].equationString);
            }
        }
    }
}

function createEqFilters(globalVars) {
    globalVars["eqFilters"] = {}
    parseEquations(globalVars)

    for (let key in globalVars["parsedEquations"]) {
        let eq = globalVars["parsedEquations"][key]
        let filter = (findEqFilter(eq, key, globalVars))[1];
        if (filter) {
            if (filter.length > 0) {
                globalVars["eqFilters"][key + "FFilter"] = "";
                for (let fil of filter) {
                    if (globalVars["eqFilters"][key + "FFilter"].length > 0) {
                        globalVars["eqFilters"][key + "FFilter"] = globalVars["eqFilters"][key + "FFilter"] + ".*(" + fil + ")";
                    } else {
                        globalVars["eqFilters"][key + "FFilter"] = "(" + fil + ")";
                    }
                }
            }
        }
    }
}

function findEqFilter(eq, eqName, globalVars) {
    let result = false

    var newEq = eq.transform(function (node, path, parent) {
        if (node.isSymbolNode) {
            if ((globalVars["parsedEquations"][node.name] != undefined) && (node.name != eqName)) {
                let res = findEqFilter(globalVars["parsedEquations"][node.name], node.name, globalVars);
                if (res[1]) {
                    if (result) {
                        for (let fil of res[1]) {
                            if (!(result.includes(fil))) {
                                result.push(fil)
                            }
                        }
                    } else {
                        result = [];
                        for (let fil of res[1]) {
                            result.push(fil)
                        }
                    }
                }
                return res[0];
            } else if (globalVars["globalScopedata"].get(node.name) != undefined) {
                if (globalVars["tagFilters"].hasOwnProperty(node.name + "FFilter"))
                    if (result) {
                        result.push(node.name + "FFilter");
                    } else {
                        result = [];
                        result.push(node.name + "FFilter");
                    };
                if (globalVars["globalScopedata"].get(node.name) != undefined) {    
                    return new mathjs.ConstantNode(globalVars["globalScopedata"].get(node.name))
                }
            }
        }
        return node
    })

    return [newEq["value"], result];
}

function parseEquations(globalVars) {
    globalVars["parsedEquations"] = {};

    for (const [k, d] of globalVars["globalEquations"].entries()) {
        try {
            eq = mathjs.parse(d);
            globalVars["parsedEquations"][eq.name] = eq;
        } catch (e) {
            if (!globalVars.isLambda) {
                globalVars.WarningMessage = globalVars.WarningMessage+"\n"+"error parsing:"+d
            }
            logRecorder("error parsing:"+d, globalVars);
        }
    }
}

function setScope(globalVars) {
    if (globalVars["RTcalcSetup"].Constant.length > 0) {
        for (var i = 0; i < globalVars["RTcalcSetup"].Constant.length; i++) {
            var tmpn = Number(globalVars["RTcalcSetup"].Constant[i].value);
            if (isFinite(tmpn) && !isNaN(tmpn)) {
                globalVars["globalScopedata"].set(globalVars["RTcalcSetup"].Constant[i].variable, tmpn);
            } else {
                if (!globalVars.isLambda) {
                    globalVars.WarningMessage = globalVars.WarningMessage+"\n"+"error: constant is infinite/Nan"
                }
                logRecorder("error: constant is infinite/Nan", globalVars);
            }
        }
    }
}

function prepareAllQueries(globalVars) {
    let queries = new Map();
    let customQueries = new Map();

    for (var i = 0; i < globalVars["RTcalcSetup"].Array.length; i++) {
        try {
            prepareQuery(config.servers[globalVars["RTcalcSetup"].Array[i].readFrom].database + ";" + globalVars["RTcalcSetup"].Array[i].table, globalVars["RTcalcSetup"].Array[i], queries);
        } catch (e) {
            if (!globalVars.isLambda) {
                    globalVars.WarningMessage = globalVars.WarningMessage+"\n"+"Error querying normal query from "+globalVars["RTcalcSetup"].Array[i].readFrom+", Error = "+e
                }
            logRecorder("Error querying normal query from "+globalVars["RTcalcSetup"].Array[i].readFrom+", Error = "+e, globalVars)
        }
    }

    if (globalVars["RTcalcSetup"].Query != undefined) {
        for (var i = 0; i < globalVars["RTcalcSetup"].Query.length; i++) {
            try {
                prepareQuery(config.servers[globalVars["RTcalcSetup"].Query[i].readFrom].database + ";" + globalVars["RTcalcSetup"].Query[i].variable+";"+globalVars["RTcalcSetup"].Query[i].writeTo, globalVars["RTcalcSetup"].Query[i], queries); // [database to query from];[Tag];[database to write to] ------ Might need to tidy the string and the logic up to make things more readable
                if (globalVars["RTcalcSetup"].Query[i].isShift != undefined) {
                    if (globalVars["RTcalcSetup"].Query[i].isShift) {
                        globalVars["shiftedKeys"].push(globalVars["RTcalcSetup"].Query[i].variable)
                    }
                }
            } catch (e) {
                if (!globalVars.isLambda) {
                    globalVars.WarningMessage = globalVars.WarningMessage+"\n"+"Error querying query var from "+globalVars["RTcalcSetup"].Query[i].readFrom+", Error = "+e
                }
                logRecorder("Error querying query var from "+globalVars["RTcalcSetup"].Query[i].readFrom+", Error = "+e, globalVars)
            }
        }
    }

    return queries;
}

function prepareQuery(queryKey ,queryInfoObj, queries) {
    if (queries.has(queryKey)) {
        let ta = queries.get(queryKey);
        ta.push(queryInfoObj)
    }
    else {
        let ta2 = new Array();
        ta2.push(queryInfoObj);
        queries.set(queryKey, ta2);
    }
}

const startRTcalculation = async (logObj, isWriteBack = true, isLogReturned = false, isDataReturned = false, pageName, currentConfig, isLambda = false) => { 
    let result = {}
    let globalVars = {
        opServerOutput: [],
        opServerOutputParsed: [],
        opServerInput: [],
        opServerResults: "",
        serverLogToReturn: "",
        globalScopedata: new Map(),
        globalScopeLastValues: {},
        globalEquations: new Map(),
        globalEquationTime: new Map(),
        globalTimestamp: [],
        RTcalcSetup: {},
        calcOneEqErrors: [],
        Servers: new Map(),
        writeServers: new Map(),
        tagFilters: {},
        eqFilters: {},
        parsedEquations: {},
        shiftedKeys: [],
        ErrorMessage: "",
        WarningMessage: "",
        logMessage: "",
        isLambda: isLambda
    } // Made them local so that multiple instances of the function can be called without affecting already running instances

    logRecorder("Initialising PIQ INTELLECT for "+pageName, globalVars)
    if (!globalVars.isLambda) {
                    globalVars.logMessage = globalVars.logMessage+"\n"+"Initialising PIQ INTELLECT for "+pageName
                }
    globalVars["RTcalcSetup"] = currentConfig;
    try{
        result[pageName] = {}
        connectAllServers (globalVars, isLambda)
        let startTime = moment().utc();
        setScope(globalVars);
        initialiseArrays(globalVars);
        getTagFilters(globalVars)
        createEqFilters(globalVars);

        let qresults = {}
        qresults["map"] = {influx: new Map(), mysql:  new Map()}
        qresults["obj"] = {}

        if (influxQueriesEnabled) {
            let tagInfo = prepareAllQueries(globalVars);
            let timeFromStr = currentConfig.timeFrom!=undefined?
                ((isNaN(currentConfig.timeFrom) || currentConfig.timeFrom == false) ?
                    (currentConfig.timeFrom == false?
                        currentConfig.timeFrom
                        :parseInfluxTimeExpression (currentConfig.timeFrom))
                    :currentConfig.timeFrom+"s")
                :false;
            let timeToStr = currentConfig.timeTo!=undefined?
                ((isNaN(currentConfig.timeTo) || currentConfig.timeTo == false) ?
                    (currentConfig.timeTo == false?
                        currentConfig.timeTo
                        :parseInfluxTimeExpression (currentConfig.timeTo))
                    :currentConfig.timeTo+"s")
                :false;

            qresults = await queryDatabase(tagInfo, currentConfig.Site+"Analytic", currentConfig.avgOVer, timeFromStr, timeToStr, currentConfig.periodForContinious, isWriteBack, globalVars);
            processQueryResult(qresults["map"], tagInfo, globalVars);
        } else if (opcUAReadEnabled) {
            let opcReadData = await opcUARead (globalVars)
            globalVars["globalTimestamp"].push(startTime)

            for (let key in opcReadData) {
                var tmpn = Number(opcReadData[key]);
                if (isFinite(tmpn) && !isNaN(tmpn)) {
                    globalVars["globalScopedata"].set(key, tmpn);
                } else {
                    if (!globalVars.isLambda) {
                    globalVars.WarningMessage = globalVars.WarningMessage+"\n"+"error: constant read from OPC is infinite/Nan"
                }
                    logRecorder("error: constant read from OPC is infinite/Nan", globalVars);
                }
            }
        }

        let eqResults = calcEachEquation({}, globalVars, logObj);
        eqResults = await optimisationHandler(currentConfig, eqResults, qresults["map"], globalVars)
        eqResults = await rayHandler(currentConfig, eqResults, qresults["map"], globalVars)
        eqResults = calcEachEquation(eqResults, globalVars);
        let finalEqResult = filterAllEquations(eqResults, globalVars);
        if (isWriteBack) {
            writeData(finalEqResult, globalVars);
            await opcUAWrite(finalEqResult, qresults["obj"], globalVars)
            await modbusWrite(finalEqResult, qresults["obj"], globalVars)
        }

        globalVars["globalScopedata"].clear();
        globalVars["globalScopedata"].clear();
        globalVars["globalEquationTime"].clear();// = new Map();
        globalVars["globalTimestamp"] = [];

        logRecorder("Iteration time: "+ (moment().utc()-startTime)+" ms", globalVars)
        if (!globalVars.isLambda) {
                    globalVars.logMessage = globalVars.logMessage+"\n"+"Iteration time: "+ (moment().utc()-startTime)+" ms"
                }

        disconnectAllServers(globalVars);

        if (isLogReturned) {
            result[pageName]["log"] = globalVars["serverLogToReturn"]
            result[pageName]["opServerInput"] = globalVars["opServerInput"]
            result[pageName]["opServerResults"] = globalVars["opServerResults"]
            result[pageName]["opServerOutput"] = globalVars["opServerOutput"]
            result[pageName]["SERVEOutput"] = globalVars["SERVEOutput"]
            result[pageName]["opServerOutputParsed"] = globalVars["opServerOutputParsed"]
        }

        if (isDataReturned) {
            result[pageName]["eqData"] = finalEqResult
            result[pageName]["qData"] = qresults["obj"]
        }
        
    } catch (e) {
        logRecorder(e, globalVars)
        if (!globalVars.isLambda) {
            globalVars.ErrorMessage = globalVars.ErrorMessage+"\n"+e
        }
        globalVars["globalScopedata"].clear();
        globalVars["globalEquations"].clear();
        globalVars["globalEquationTime"].clear();// = new Map();
        globalVars["globalTimestamp"] = [];
        disconnectAllServers(globalVars);
    }
    
    fs.appendFileSync(contLogsFolder+'/Error.txt', globalVars.ErrorMessage);
    fs.appendFileSync(contLogsFolder+'/Warning.txt', globalVars.WarningMessage);
    fs.appendFileSync(contLogsFolder+'/log.txt', globalVars.logMessage);

    return result
}

async function rayHandler(conf, eqResults, qResults, globalVars) {
    try {
        if (conf["Serve"] != undefined && conf["Serve"].length > 0) {
	        console.log("rayHandler")
            let rayQueries = []
            for (let rayVar of conf["Serve"]) {
                
                if(rayVar['variable'] != undefined &&
                    rayVar['serveString'] != undefined){
                    
                    let rayQuery = {}
                    var PREFIX = "SERVE";
                    if (rayVar['variable'].startsWith(PREFIX)) {
                        rayVar['variable'] = rayVar['variable'].slice(PREFIX.length);
                    }
                    rayQuery['variable'] = rayVar['variable']
                    let tmp = rayVar['serveString'].split(';')
                    rayQuery['model_id'] = tmp[tmp.length-1]
                    rayQuery['features'] = tmp.slice(0, -1)
                    // console.log("model_id", rayQuery['model_id'])
                    // console.log("features", rayQuery['features'])
                    
                    let data = [];
                    // queries
                    for (let k in qResults) {
                        for (const [k1, d] of qResults[k].entries()) {
                            for (let k2 in d) {
                                for (let k3 in d[k2]) {
                                    if (k3 != 'time' && d[k2]['time'] != undefined && rayQuery["features"].includes(k3)) {
                                        if (data[k2] == undefined) {
                                            data.push({});
                                        }
                                        if (data[k2][k3] != undefined) {
                                            data[k2][k3].push(convertBoolToVal(d[k2][k3]))
                                        } else {
                                            data[k2][k3] = convertBoolToVal(d[k2][k3])
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // equation (arrays)
                    for (let k in eqResults) {
                        if (rayQuery["features"].includes(k)) {
                            if (eqResults[k].length != undefined) {
                                for (let j in eqResults[k]) {
                                    if (data[j] == undefined) {
                                        data.push({});
                                    }
                                    data[j][k] = convertBoolToVal(eqResults[k][j]);
                                }
                            }
                        }
                    }

                    // equation (constants)
                    // including constants last, or might not get included if the constant is being searched first and datas is []
                    for (let k in eqResults) { 
                        if (rayQuery["features"].includes(k)) {
                            if (eqResults[k].length == undefined) {
                                for (let j in data) {
                                    data[j][k] = convertBoolToVal(eqResults[k]);
                                }
                            }  
                        }
                    }

                    // // remove datapoints with NaN values
                    // let dataNoNull = []
                    // for(let obj of data){
                    //     let NaNpresent = false
                    //     for(let [_, val] of Object.entries(obj)){
                    //         if(!val || isNaN(val)) NaNpresent = true
                    //     }
                    //     if(!NaNpresent) dataNoNull.push(obj)
                    // }

                    // Transpose data matrix
                    let dataTransposed = {}
                    for(let [i, feature] of rayQuery['features'].entries()){
                        // console.log("i, varDep", i, varDep)
                        // let feature_name = `x${i+1}`
                        let feature_index = i.toString()
                        if(dataTransposed[feature_index] == undefined){
                            dataTransposed[feature_index] = []
                        }
                        if(data.length > 0){
                            for(let obj of data){
                                dataTransposed[feature_index].push(obj[feature])
                            }
                        }
                    }

                    rayQuery["data"] = dataTransposed;
                    rayQueries.push(rayQuery)
                } 
                
            }
            for(let rayQuery of rayQueries){
                ///////////////////////////////////////////////////////////////////
                // fs.writeFileSync('rayQueries.json', JSON.stringify(rayQueries, null, 4), 'utf8'); // debugging
                ///////////////////////////////////////////////////////////////////
                eqResults = await sendToRayServer(rayQuery, eqResults, globalVars)
            }
        }
    } catch (e) {
        console.log("error from rayHandler: " + e)
	    console.log("stacktrace: " + e.stack)
    }

    return eqResults
}

const http = require('http');
async function sendToRayServer(rayQuery, eqResults, globalVars) {
    return new Promise (resolve => {
        try {
            var req = https.request(config.rayServerOptions, function(res) {
                res.setEncoding('utf8');
                let body = '';
                res.on('data', function (chunk) {
                    body += chunk
                })
                res.on('end', function () {
                    ///////////////////////////////////////////////////////////////////
                    // fs.writeFileSync('rayResponse.json', body, 'utf8'); // debugging
                    ///////////////////////////////////////////////////////////////////
                    try{
                        let resArray = JSON.parse(body);
                        if (resArray["success"] == true) {
                            if(globalVars["SERVEOutput"] == undefined){
                                globalVars["SERVEOutput"] = []
                            }
                            globalVars["SERVEOutput"].push({[rayQuery["variable"]]: resArray["data"]}) // seen in calc panel "eqn results"
                            // console.log("variable", rayQuery["variable"])
                            let eq = mathjs.parse(rayQuery["variable"]+"="+JSON.stringify(resArray["data"]));
                            globalVars["parsedEquations"][rayQuery["variable"]] = eq // allows results to be used in future equation calculation
                            eqResults[rayQuery["variable"]] = resArray["data"] // allows write back
                        } else {
                            console.log("ray error response: ", body)
                        }
                        resolve(eqResults)
                    } catch(err){
                        console.log("ray error: ", err)
                        console.log("ray stacktrace: ", err.stack)
                        resolve(eqResults)
                    }
                })
            });
            req.on('error', function(err) {
                console.log("ray error: ", err)
		        console.log("ray stacktrace: ", err.stack)
                resolve(eqResults)
            });
            var data = JSON.stringify(rayQuery)
            req.end(data);
        } catch (err) {
            console.log("ray error: ", err)
	        console.log("ray stacktrace: ", err.stack)
            resolve(eqResults)
        }
    })
} 

async function optimisationHandler(conf, eqResults, qResults, globalVars) {
    try {
        if (conf["Object"] != undefined && conf["Object"].length > 0) {            
            let optimisationQueries = [];

            console.log("conf[object]", conf["Object"])
            for (let obj of conf["Object"]) {

                if (obj["value"] != undefined) {
                    let opQuery = JSON.parse(JSON.stringify(obj.value)) // to clone
                    console.log("opQuery", opQuery)
                    opQuery["name"] = obj.variable

                    let datas = [];
                    // data from query results
                    for (let k in qResults) {
                        for (const [k1, d] of qResults[k].entries()) {
                            for (let k2 in d) {
                                for (let k3 in d[k2]) {
                                    if (k3 != 'time' && d[k2]['time'] != undefined && opQuery["objective"].includes(k3) && !opQuery["variables"].includes(k3)) {
                                        if (datas[k2] == undefined) {
                                            datas.push({});
                                        }
                                        datas[k2][k3] = convertBoolToVal(d[k2][k3])
                                    }
                                }
                            }
                        }
                    }
 
                    // data from equation results (arrays)
                    for (let k in eqResults) {
                        if (opQuery["objective"].includes(k) && !opQuery["variables"].includes(k)) {
                            if (eqResults[k].length != undefined) {
                                for (let j in eqResults[k]) {
                                    if (datas[j] == undefined) {
                                        datas.push({});
                                    }
                                    datas[j][k] = convertBoolToVal(eqResults[k][j]);
                                }
                            }
                        }
                    }

                    // data from equation results (constants)
                    for (let k in eqResults) {
                        if (opQuery["objective"].includes(k) && !opQuery["variables"].includes(k)) {
                            if (eqResults[k].length == undefined) {
                                for (let j in datas) {
                                    datas[j][k] = convertBoolToVal(eqResults[k]);
                                }
                            }  
                        }
                    }

                    // expand constraints
                    for (let k in opQuery["constraints"]) {
                        let prepEq = opQuery["constraints"][k]

                        let newEq = mathjs.parse(prepEq).transform(function (node, path, parent) {
                            if (node.isSymbolNode) {
                                if (eqResults[node.name] != undefined) {
                                    opQuery["constraints"][k] = opQuery["constraints"][k].replace(node.name, eqResults[node.name])
                                    return node
                                }
                            }
                            return node
                        })
                    }

                    opQuery["datas"] = datas;
                    optimisationQueries.push(opQuery)
                }
            }

            ///////////////////////////////////////////////////////////////////
            //fs.writeFileSync('test.json', JSON.stringify(optimisationQueries), 'utf8'); // debugging
            ///////////////////////////////////////////////////////////////////

            for(let opQuery of optimisationQueries){
                console.log("opQuery", opQuery)
                eqResults = await sendToOPServer(opQuery, eqResults, globalVars)
            }
        }
    } catch (e) {
        console.log("error from objectHandler: "+e)
        console.log(e.stack)
    }

    return eqResults
}

function convertBoolToVal (val) {
    res=val;
    if (val == true) {
        res = 1;
    } else if (val == false) {
        res = 0;;
    }

    return res
}

async function sendToOPServer(opQuery, eqResults, globalVars) {
    return new Promise (resolve => {
        try {
            var req = https.request(config.rayTuneOptions, function(res) {
                res.setEncoding('utf8');
                let body = '';
                res.on('data', function (chunk) {
                    body += chunk
                })
                res.on('end', function (chunk) {
                    try {
                        let resArray = JSON.parse(body); 
                        // if (resArray["success"] == true) {
                        //     console.log("resArray[data]", resArray["data"])
                        //     resArray["data"] = resArray["data"].filter(x => x) // remove null
                        //     const average = array => array.reduce((a, b) => a + b) / array.length;
                        //     for(let variable of opQuery["variables"]){
                        //         var res_variable = average(resArray["data"].map(x => x[variable]))
                        //         let eq = mathjs.parse(variable+"="+JSON.stringify(res_variable));
                        //         globalVars["parsedEquations"][variable] = eq
                        //         eqResults[variable] = res_variable
                        //         console.log("sendToOPServer", variable, res_variable)
                        //     }
                        // }
                        if (resArray["success"] == true) {
                            if(globalVars["opServerOutput"] == undefined){
                                globalVars["opServerOutput"] = []
                            }
                            globalVars["opServerOutput"].push({[opQuery["name"]]: resArray["data"]}) // seen in calc panel "eqn results"
                            globalVars["opServerResults"] += `
================================
    Decision Variables
================================`
                            for(let variable of opQuery["variables"]){
                                let eq = mathjs.parse(variable+"="+JSON.stringify(resArray["data"][variable]));
                                globalVars["parsedEquations"][variable] = eq // allows results to be used in future equation calculation
                                eqResults[variable] = resArray["data"][variable] // allows write back
                                globalVars["opServerResults"] += "\n"+variable+"="+resArray["data"][variable] // seen in calc panel logs
                                console.log("sendToOPServer", variable, resArray["data"][variable])
                            }
                            globalVars["opServerResults"] += "\n _________________________________________"
                        } else {
                            console.log("opserver response: ", body)
                        }
                        resolve(eqResults)
                    } catch (err) {
                        console.log("opserver error: ", err)
                        console.log("opserver stacktrace: ", err.stack)
                        resolve(eqResults)
                    }
                });
            });
            req.on('error', function(err) {
                console.log("opserver error: ", err)
		        console.log("opserver stacktrace: ", err.stack)
                resolve(eqResults)
            });
            var data = JSON.stringify(opQuery)
            req.end(data);
        } catch (err) {
            console.log("opserver error: ", err)
	        console.log("opserver stacktrace: ", err.stack)
            resolve(eqResults)
        }
    });
}

function calcEachEquation(eqResults, globalVars, logObj) {
    globalVars["calcOneEqErrors"] = [];
    const eqs = globalVars["parsedEquations"]
   

    if (eqs && logObj) {
        //console.log(JSON.stringify(globalVars))
        logObj.message = `${logObj.message}, Variables: ${JSON.stringify(globalVars["RTcalcSetup"]["Array"])}, Equations: ${JSON.stringify(globalVars["RTcalcSetup"]["Equation"])}`
        
       
    }

    for (var key in globalVars["parsedEquations"]) {
        try {
            if (eqResults [key] != undefined) {
                if (eqResults[key] == "Infinity" || eqResults[key] == Infinity) {
                    delete eqResults[key]
                } else if (typeof eqResults[key] == 'object') {
                    if (eqResults[key].includes("Infinity") || eqResults[key].includes(Infinity)) {
                        delete eqResults[key]
                    }
                }
            }
        } catch (e) {
            console.log("Error deleting results when recalculating: "+ e)
        }

        calcOneEquation(key, eqResults, true, globalVars);
    }
    for (let e of globalVars["calcOneEqErrors"] ) {
    if (!globalVars.isLambda) {
        globalVars.WarningMessage= globalVars.WarningMessage+"\n"+e
    }
        logRecorder(e, globalVars)
    }
    return eqResults
}

function calcOneEquation(eqName, eqResults, isHighLevel = false, globalVars) {
    let ignoreScope = {Infinityi: Infinity, NaNi: Infinity} 

    try {
        if (eqResults[eqName] != undefined) {
            return new mathjs.ConstantNode(eqResults[eqName])
        } else {
            var newEq = globalVars["parsedEquations"][eqName].transform(function (node, path, parent) {
                if (node.isSymbolNode) {
                    if ((globalVars["parsedEquations"][node.name] != undefined) && (node.name != eqName)) {
                        let tmpre = calcOneEquation(node.name, eqResults, false, globalVars);
                        return tmpre;
                    } else if (globalVars["globalScopedata"].get(node.name) != undefined) {
                        let conststr = new mathjs.ConstantNode(globalVars["globalScopedata"].get(node.name));
                        return conststr;
                    } else if (!mathjsFuncIgnoreList.includes(node.name)) {
                        ignoreScope[node.name] = Infinity;
                    }
                }
                
                return node
            })

            let rr = convertTreeToString({ 0: newEq })["0"];

            try {
                let tmpva = mathjs.evaluate(rr, ignoreScope);
                tmpva = (tmpva._data == undefined) ? tmpva : tmpva._data;
                eqResults[eqName] = tmpva
            } catch (err) {
                if (! globalVars["calcOneEqErrors"] .includes("ERR in calcOneEquation function = "+globalVars["parsedEquations"][eqName]+" "+err)) {
                   globalVars["calcOneEqErrors"] .push("ERR in calcOneEquation function = "+globalVars["parsedEquations"][eqName]+" "+err)
                }
            }

            return newEq["value"];
        }
    }
    catch (err) {
        if (! globalVars["calcOneEqErrors"] .includes("ERR in calcOneEquation() = " + err)) {
           globalVars["calcOneEqErrors"] .push("ERR in calcOneEquation() = " + err)
        }
    }
}

function initialiseArrays(globalVars) {
    for (let arr of globalVars["RTcalcSetup"].Array) {
        globalVars["globalScopedata"].set(arr.variable, [])
    }
}


function filterAllEquations(eqResults, globalVars) {
    for (let key in Object.assign({}, globalVars["globalScopedata"], eqResults)) {

        let filter = false;
        if (globalVars["eqFilters"][key + "FFilter"] != undefined) {
            try {
                filter = mathjs.evaluate(globalVars["eqFilters"][key + "FFilter"], eqResults);
            } catch (e) {
                logRecorder("Error evaluating filter for "+key, globalVars)
            }
            // console.log("filter = ", filter);
        }

        eqResults[key] = (Array.isArray(eqResults[key]) && (eqResults[key].length > 1)) ? filterScopeVar(eqResults[key], filter, key, globalVars) : eqResults[key];
    }


    return eqResults
}


function writeInfluxHelper(dbstr, jsonData, globalVars) {
    let jsonArr = JSON.parse(jsonData);

    if (jsonArr.length > 1 && !(opcUAWriteEnabled || opcUAReadEnabled)) {
        jsonArr.splice(0,1)
    }
    
    if (jsonArr.length > 0) {
        const batchSize = 100; // number or records per batch
        let batchesSent = 0; // number of batches sent
        let measurements = []; // all measurements to send
        for (j = 0; j < jsonArr.length; j++) {
            try {
                measurements.push({
                    measurement: config.servers[dbstr].table,
                    fields: jsonArr[j],
                    timestamp: jsonArr[j]['Timestamp2']
                });
            } catch (e) {
                // logRecorder('error writing back: '+e, globalVars);
            }
        }
        let influxclient = globalVars["writeServers"].get(config.servers[dbstr]);
        sendBatchToInflux(influxclient, measurements, 0, batchSize, batchesSent, globalVars).then(() => {
        }).catch((err) => {
            if (!globalVars.isLambda) {
            globalVars.WarningMessage = globalVars.WarningMessage+"\n"+'ERROR (sendBatchToInflux) '+ err
        }
            logRecorder('ERROR (sendBatchToInflux) '+ err, globalVars);
        });
    } else {
        if (!globalVars.isLambda) {
            globalVars.WarningMessage = globalVars.WarningMessage+"\n"+'processMeasurements: No data in jsonArr (i.e. no data in zip)'
        }
        logRecorder('processMeasurements: No data in jsonArr (i.e. no data in zip)', globalVars);
    }
}

function sendBatchToInflux(influxclient, measurements, startRec, batchSize, batchesSent, globalVars) {
    return new Promise((resolve, reject) => {
        let batchRecs = measurements.slice(startRec, (startRec + batchSize));
        if (batchRecs.length > 0) {
            if (!globalVars.isLambda) {
                globalVars.logMessage= globalVars.logMessage+"\n"+'Send batch ' + batchesSent + ' (' + startRec + '-' + (startRec + batchSize - 1) + ')'
            }
            console.log('Send batch ' + batchesSent + ' (' + startRec + '-' + (startRec + batchSize - 1) + ')');
            influxclient.writePoints(
                batchRecs
            ).then(() => {
                batchesSent++;
                sendBatchToInflux(influxclient, measurements, (startRec + batchSize), batchSize, batchesSent, globalVars).then(() => {
                    resolve();
                }).catch((err) => {
                    if (!globalVars.isLambda) {
                        globalVars.WarningMessage= globalVars.WarningMessage+"\n"+"ERROR (sendBatchToInflux inner promise) "+ err
                    }
                    console.log("ERROR (sendBatchToInflux inner promise)", err);
                    reject(err);
                });
            }).catch((err) => {
                if (!globalVars.isLambda) {
                        globalVars.WarningMessage= globalVars.WarningMessage+"\n"+"ERROR (sendBatchToInflux inner promise) "+ err
                    }
                console.log("ERROR (sendBatchToInflux promise)", err);
                reject(err);
            });
        } else {
            if (!globalVars.isLambda) {
                        globalVars.logMessage= globalVars.logMessage+"\n"+'All Done. Time of log: '+moment().utc().format("YYYY-MM-DDTHH:mm:ss[Z]").toString()
                    }
            logRecorder('All Done. Time of log: '+moment().utc().format("YYYY-MM-DDTHH:mm:ss[Z]").toString(), globalVars);
            resolve();
        }
    });
}

function writeMqttHelper(server, jsonData, globalVars) {
    var mqttServer = globalVars["Servers"].get(server);
    mqttServer.publish(serverstr.controlTopic, JSON.stringify(jsonData), (err) => {
        console.log(err || 'Publish Success')
    })
}

const writeToDatabase = async (dbstr, timepoints, globalVars) => {
    let dataarr = [];
    switch (config.servers[dbstr].type) {
        case "influx":
            for (k in timepoints)
                dataarr.push(timepoints[k]);
            
            writeInfluxHelper(dbstr, JSON.stringify(dataarr), globalVars);
            break;
        case "mqtt":
            for (k in timepoints)
                dataarr.push(timepoints[k]);

            writeMqttHelper(config.servers[dbstr], dataarr, globalVars);
            break;
        default:
            console.log("ERROR: CANNOT BE  at writeToDatabase switch");
            break;
    }
}

function writeData(finalEqResult, globalVars) {
    try {
        let errorMessages = []
        let writeto = {};
        let timestamp = {};

        for (var j = 0; j < globalVars["RTcalcSetup"].Constant.length; j++) {
            if (allowedWriteDataBases.includes(globalVars["RTcalcSetup"].Constant[j].writeTo) && writeto[globalVars["RTcalcSetup"].Constant[j].writeTo] == undefined) {
                writeto[globalVars["RTcalcSetup"].Constant[j].writeTo] = {};
                for (var i = 0; i < globalVars["globalTimestamp"].length; i++) {
                    writeto[globalVars["RTcalcSetup"].Constant[j].writeTo][globalVars["globalTimestamp"][i]] = {'Timestamp2': globalVars["globalTimestamp"][i] * 1000000}
                }
            }
        }

        for (var k = 0; k < globalVars["RTcalcSetup"].Equation.length; k++) {
            if (allowedWriteDataBases.includes(globalVars["RTcalcSetup"].Equation[k].writeTo) && writeto[globalVars["RTcalcSetup"].Equation[k].writeTo] == undefined) {
                writeto[globalVars["RTcalcSetup"].Equation[k].writeTo] = {};
                for (var i = 0; i < globalVars["globalTimestamp"].length; i++) {
                     writeto[globalVars["RTcalcSetup"].Equation[k].writeTo][globalVars["globalTimestamp"][i]] = {'Timestamp2': globalVars["globalTimestamp"][i] * 1000000}
                }
            }
        }

        for (var i = 0; i < globalVars["globalTimestamp"].length; i++) {
            for (var j = 0; j < globalVars["RTcalcSetup"].Constant.length; j++) {
                if (allowedWriteDataBases.includes(globalVars["RTcalcSetup"].Constant[j].writeTo)) {
                    var tmpn = Number(globalVars["RTcalcSetup"].Constant[j].value);
                    if (isFinite(tmpn) && !isNaN(tmpn))
                        writeto[globalVars["RTcalcSetup"].Constant[j].writeTo][globalVars["globalTimestamp"][i]][globalVars["RTcalcSetup"].Constant[j].variable] = tmpn;
                } else if (!globalVars["RTcalcSetup"].Constant[j].variable.includes("Alarm") && !globalVars["RTcalcSetup"].Constant[j].variable.includes("opTempVar")) {
                    let m = "The write database "+globalVars["RTcalcSetup"].Constant[j].writeTo+" for "+globalVars["RTcalcSetup"].Constant[j].variable+" is not included in \"allowedWriteDataBases\""
                    if (! errorMessages.includes(m)) {
                        errorMessages.push(m)
                    }
                }
            }

            for (var k = 0; k < globalVars["RTcalcSetup"].Equation.length; k++) {
                if (allowedWriteDataBases.includes(globalVars["RTcalcSetup"].Equation[k].writeTo)) {
                    if (globalVars["RTcalcSetup"].Equation[k].writeTo != "none") {
                        let keysrt = globalVars["RTcalcSetup"].Equation[k].equation;
                        if (Array.isArray(finalEqResult[keysrt]) && finalEqResult[keysrt].length && (globalVars["globalEquationTime"].get(keysrt) != undefined)) {
                            var tmpn = finalEqResult[keysrt][globalVars["globalEquationTime"].get(keysrt).indexOf(globalVars["globalTimestamp"][i])];
                            if (isFinite(Number(tmpn)) && !isNaN(Number(tmpn))) {
                                writeto[globalVars["RTcalcSetup"].Equation[k].writeTo][globalVars["globalTimestamp"][i]][keysrt] = Number(tmpn);
                            } else if (Array.isArray(tmpn)) {
                                writeto[globalVars["RTcalcSetup"].Equation[k].writeTo][globalVars["globalTimestamp"][i]][keysrt] = JSON.stringify(tmpn);
                            }
                        }
                        else {
                            if (finalEqResult[keysrt] != undefined) {
                                var tmpn = finalEqResult[keysrt];

                                if (isFinite(Number(tmpn)) && !isNaN(Number(tmpn))) {
                                    writeto[globalVars["RTcalcSetup"].Equation[k].writeTo][globalVars["globalTimestamp"][i]][keysrt] = Number(tmpn);
                                } else if (Array.isArray(tmpn)) {
                                    writeto[globalVars["RTcalcSetup"].Equation[k].writeTo][globalVars["globalTimestamp"][i]][keysrt] = JSON.stringify(tmpn);
                                }
                            }
                        }
                    }
                } else if (!globalVars["RTcalcSetup"].Equation[k].equation.includes("Alarm") && !globalVars["RTcalcSetup"].Equation[k].equation.includes("opTempVar")) {
                    let m = "The write database "+globalVars["RTcalcSetup"].Equation[k].writeTo+" for "+globalVars["RTcalcSetup"].Equation[k].equation+" is not included in \"allowedWriteDataBases\""
                    if (! errorMessages.includes(m)) {
                        errorMessages.push(m)
                    }
                }
            }

            if(globalVars["RTcalcSetup"]["Object"] != undefined){
                for (var j = 0; j < globalVars["RTcalcSetup"].Object.length; j++) {
                    if (allowedWriteDataBases.includes(globalVars["RTcalcSetup"].Object[j].writeTo)) { // accidently assigned writeto value to readto on front end, change this when corrected on frontend side
                        let obj = globalVars["RTcalcSetup"].Object[j].value

                        for (let z in obj.variables) {
                            let oVar = obj.variables[z];
                            var tmpn = Number(finalEqResult[oVar]);
                            if (isFinite(tmpn) && !isNaN(tmpn)) {
                                writeto[globalVars["RTcalcSetup"].Object[j].writeTo][globalVars["globalTimestamp"][i]][oVar] = tmpn;
                            } else if (Array.isArray(finalEqResult[oVar])) { 
                                writeto[globalVars["RTcalcSetup"].Object[j].writeTo][globalVars["globalTimestamp"][i]][oVar] = JSON.stringify(finalEqResult[oVar][i]);
                            }
                        }

                    } else if (!globalVars["RTcalcSetup"].Object[j].variable.includes("Alarm") && !globalVars["RTcalcSetup"].Object[j].variable.includes("opTempVar")) {
                        let m = "The write database "+globalVars["RTcalcSetup"].Object[j].writeTo+" for "+globalVars["RTcalcSetup"].Object[j].variable+" is not included in \"allowedWriteDataBases\""
                        if (! errorMessages.includes(m)) {
                            errorMessages.push(m)
                        }
                    }
                }
            }

            if(globalVars["RTcalcSetup"]["Serve"] != undefined){
                for (var k = 0; k < globalVars["RTcalcSetup"]["Serve"].length; k++) {
                    if (allowedWriteDataBases.includes(globalVars["RTcalcSetup"]["Serve"][k].writeTo)) {
                        if (globalVars["RTcalcSetup"]["Serve"][k].writeTo != "none") {
                            let keysrt = globalVars["RTcalcSetup"]["Serve"][k].variable;
                            if (Array.isArray(finalEqResult[keysrt]) && finalEqResult[keysrt].length && (globalVars["globalEquationTime"].get(keysrt) != undefined)) {
                                var tmpn = finalEqResult[keysrt][globalVars["globalEquationTime"].get(keysrt).indexOf(globalVars["globalTimestamp"][i])];
                                if (isFinite(Number(tmpn)) && !isNaN(Number(tmpn))) {
                                    writeto[globalVars["RTcalcSetup"]["Serve"][k].writeTo][globalVars["globalTimestamp"][i]][keysrt] = Number(tmpn);
                                } else if (Array.isArray(tmpn)) {
                                    writeto[globalVars["RTcalcSetup"]["Serve"][k].writeTo][globalVars["globalTimestamp"][i]][keysrt] = JSON.stringify(tmpn);
                                }
                            }
                            else {
                                if (finalEqResult[keysrt] != undefined) {
                                    var tmpn = finalEqResult[keysrt];
                                    if (isFinite(Number(tmpn)) && !isNaN(Number(tmpn))) {
                                        writeto[globalVars["RTcalcSetup"]["Serve"][k].writeTo][globalVars["globalTimestamp"][i]][keysrt] = Number(tmpn);
                                    } else if (Array.isArray(tmpn)) {
                                        writeto[globalVars["RTcalcSetup"]["Serve"][k].writeTo][globalVars["globalTimestamp"][i]][keysrt] = JSON.stringify(tmpn);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        for (let m of errorMessages) {
            logRecorder(m, globalVars)
        }

        for (key1 in writeto) {
            var dbSev = key1.split(";");
            for (var i = 0; i < dbSev.length; i++) {
                for (key2 in config.servers) {
                    if (dbSev[i] == key2) {
                        writeToDatabase(dbSev, writeto[key2], globalVars);
                    }
                }
            }
        }
    }
    catch (err) {
        logRecorder("ERR in writeData() = " + err, globalVars);
        if (!globalVars.isLambda) {
            globalVars.ErrorMessage = globalVars.ErrorMessage+"\n"+"ERR in writeData() = " + err
        }
    }
}


module.exports = startRTcalculation
