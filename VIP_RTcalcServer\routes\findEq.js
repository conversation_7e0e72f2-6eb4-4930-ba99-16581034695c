var FormData = require('form-data');
const path = require('path');
var express = require('express');
var moment = require("moment");
var router = express.Router();
var zip = require('express-zip');
var fs = require('fs');
var util = require('util');
var jszip = require("jszip");
const archiver = require('archiver')
var multer = require('multer');
var upload = multer();
var formidable = require('formidable');
var os = require('os');
const http = require('http')
var serverName = require('./../../serverName.json');
var config = require('./../../config.json');

var calcSetupFolder =  __dirname+'/../../liveConfigs';
var configVersionsFolder =  __dirname+'/../../rtCalcConfigArchive';
var archiverTemp =  __dirname+'/../../archiverTemp';
var syncConfigFolder = __dirname+'/../../syncSettings';
var syncConfigLoc = __dirname+'/../../syncSettings/syncConfig.json';
var relayServers = []
var exec = require('child_process').exec

if (config["relayServers"] != undefined) { // need to create serverName.json file in root directory with {name://unique server name string//} 
    relayServers = config["relayServers"]
}   

router.post('/', function (req, res) {
    console.log('findEq Post');
    try {
        site = req.query.site.replace(/\s+/g, '')
        reqFolderDir = calcSetupFolder+"/"+site
        searchString = req.query.searchString
        if (fs.existsSync(reqFolderDir)) {
            fs.existsSync(reqFolderDir)
            res.header('content-type', 'text/plain')
            res.header('Access-Control-Allow-Origin', '*' )
            exec('grep '+searchString+' -rnl', 
                {cwd:reqFolderDir},
                function (error, stdout, stderr) {return res.json(stdout)})
            res.on('finish', function () {
                res.end()
            })
        }
    }
    catch (e) {
        console.log('ERROR: findEq GET - ' + e.message);
        res.writeHead(400, { 'content-type': 'text/plain', 'Access-Control-Allow-Origin': '*' });
    }
})

module.exports = router;
