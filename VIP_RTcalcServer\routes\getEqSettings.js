var FormData = require('form-data');
const path = require('path');
var express = require('express');
var moment = require("moment");
var router = express.Router();
var zip = require('express-zip');
var fs = require('fs');
var util = require('util');
var jszip = require("jszip");
const archiver = require('archiver')
var multer = require('multer');
var upload = multer();
var formidable = require('formidable');
var os = require('os');
const http = require('http')
var serverName = require('./../../serverName.json');
var config = require('./../../config.json');

var calcSetupFolder =  __dirname+'/../../liveConfigs';
var configVersionsFolder =  __dirname+'/../../rtCalcConfigArchive';
var archiverTemp =  __dirname+'/../../archiverTemp';
var syncConfigFolder = __dirname+'/../../syncSettings';
var syncConfigLoc = __dirname+'/../../syncSettings/syncConfig.json';
var relayServers = []
var exec = require('child_process').exec

if (config["relayServers"] != undefined) { // need to create serverName.json file in root directory with {name://unique server name string//} 
    relayServers = config["relayServers"]
}   

router.post('/', function (req, res) {
    console.log('getEqSettings Post');
    try {
        console.log(req.query, "del")
        site = req.query.site.replace(/\s+/g, '')
        settings = req.query.settings
        settings = JSON.parse(settings)
        reqFolderDir = calcSetupFolder+"/"+site
        if (fs.existsSync(reqFolderDir)) {
            if (!fs.existsSync(syncConfigFolder)) {
                fs.mkdirSync(syncConfigFolder)
            }

            if (!fs.existsSync(syncConfigLoc)) {
                fs.open(syncConfigLoc, 'w', function (err, file) {if (err) throw err});
                fs.writeFileSync(syncConfigLoc, "{}")
            }

            var syncConfig = require(syncConfigLoc)
            if (syncConfig[site] == undefined) {
                syncConfig[site] = {
                    allDashboards: [],
                    syncDashboards: []
                }
            }

            syncConfig[site]["allDashboards"] = []
            for (let file of fs.readdirSync(reqFolderDir)) {
                syncConfig[site]["allDashboards"].push(file)
            }

            var updatedSyncFiles = []

            for (let dash of syncConfig[site]["syncDashboards"]) {
                if (syncConfig[site]["allDashboards"].includes(dash)) {
                    updatedSyncFiles.push(dash)
                }
            }

            if (settings["toSync"] != undefined) {
                updatedSyncFiles = []
                for (let dash of settings["toSync"]) {
                    if (syncConfig[site]["allDashboards"].includes(dash)) {
                        console.log(dash)
                        updatedSyncFiles.push(dash)
                    }
                }
            }

            syncConfig[site]["syncDashboards"] = updatedSyncFiles

            fs.writeFileSync(syncConfigLoc, JSON.stringify(syncConfig))
            res.header('content-type', 'text/plain')
            res.header('Access-Control-Allow-Origin', '*' )
            res.json(syncConfig[site])
            res.on('finish', function () {
                res.end()
            })
        }
    }
    catch (e) {
        console.log('ERROR: getEqSettings Post - ' + e.message);
        res.writeHead(400, { 'content-type': 'text/plain', 'Access-Control-Allow-Origin': '*' });
        res.end('Sync settings updated for' + site);
    }
});

module.exports = router;
