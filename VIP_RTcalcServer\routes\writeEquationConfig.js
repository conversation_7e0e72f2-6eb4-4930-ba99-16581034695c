var express = require('express');
var router = express.Router();
const helperFunctions = require("./dashboardInternalAPIFunctions.js")

router.post('/', function (req, res) { // runs the calcs and writes back result to influx
    console.log('runAndWrite POST on writeEquationConfig');
    try {
        helperFunctions.recieveUnzipAndCompute(helperFunctions.run, req, res, {isWriteBack: true}).parse(req);
    } catch (e) {
    	let msg = 'ERROR: writeEquationConfig.js runAndWrite POST - ' + e.message;
        console.log(msg);
        res.end(msg)
    }
})

module.exports = router;