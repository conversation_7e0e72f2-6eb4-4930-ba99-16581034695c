﻿## Install Dependencies
```
apt install git

wget https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.2/install.sh
sudo bash install.sh
source ~/.bashrc
nvm install 16.18.0
nvm use 16.18.0
sudo rm install.sh
```

## General setup
 ```
 git clone https://<EMAIL>/processiq/vip_rtcalcserver.git
 cd vip_rtcalcserver
 npm install
 mkdir liveConfigs
 mkdir rtCalcConfigArchive

 create a file called "serverName.json" and paste in file the following
 {
 	"name": "Enter server name specified in config"
 }

 cd VIP_RTcalcServer/bin
 node VIP_RTcalcServer.js
 ```

## Lambda deploy instructions

- Zip all files in vip_rtcalcserver except for the folder "VIP_RTcalcServer"
- Log on to aws, go to Lambda.
- select the lambda instance named "serverlessrepo-simple-websocke-SendMessageFunction-o3QVoKCQEBCE" (https://ap-southeast-2.console.aws.amazon.com/lambda/home?region=ap-southeast-2#/functions/serverlessrepo-simple-websocke-SendMessageFunction-o3QVoKCQEBCE?tab=code)
- Select "Upload from" and select your zip folder

## Example for adding new client to config
https://bitbucket.org/processiq/vip_rtcalcserver/commits/3fac4a694bfe309fea520bceeaaf964d65f51c5a
