var jszip = require("jszip");
const AWS = require('aws-sdk');
const startRTcalculation = require("./CalculationFunctions.js")
const dataBatchSize = 5000;

exports.handler = async event => { 
    console.log("Lambda connection start")
    try {
        await new Promise (async function (res, rej) {
            let calcConfig = JSON.parse(event.body).data
            await jszip.loadAsync(calcConfig, {base64: true}).then(async function (unzippedFiles) {
                unzippedFiles.files[Object.keys(unzippedFiles.files)[0]].async("string").then(async function (data) {
                    let parsedData = JSON.parse(data)
                    let site = parsedData["Site"];
                    let pageName = parsedData["Dashboard"];
                    calcConfig = parsedData
                    let isWriteBack = false
                    if (calcConfig.isWriteBack != undefined) {
                        isWriteBack = calcConfig.isWriteBack
                    }
                    let result = await new Promise ((resolve, reject) => {resolve(startRTcalculation(isWriteBack, true, true, pageName, parsedData))})
                    let zipFile = jszip()
                    const apigwManagementApi = new AWS.ApiGatewayManagementApi({
                        apiVersion: '2018-11-29',
                        endpoint: event.requestContext.domainName + '/' + event.requestContext.stage
                    });
                    zipFile.file("send.zip", JSON.stringify(result));
                    zipFile.generateAsync({type:"base64", compression: "DEFLATE", compressionOptions: {level: 9}}).then(async function(content) {
                        try {
                            let batches = breakDownData (content)
                            for (i=0; i < batches.length; i++) {
                                let data = {
                                    batchNo:i,
                                    batchSize: batches.length,
                                    batch: batches[i],
                                    execId: calcConfig.execId
                                }

                                data = JSON.stringify(data)
                                await apigwManagementApi.postToConnection({ ConnectionId: event.requestContext.connectionId, Data: data }).promise();
                            }
                            return res()
                        } catch (e) {
                            console.log(e)
                            return res()
                        }
                    })
                }) 
            })
        })
    } catch (e) {
        console.log(e)
        return { statusCode: 400, body: "Error: "+e};
    }
    return { statusCode: 200, body: 'Data sent.' };
};

function breakDownData (stringData) {
    result = [];

    for (i=0; i<stringData.length; i=i+dataBatchSize) {
        result.push(stringData.slice(i, i+dataBatchSize))
    }

    return result
}

        
