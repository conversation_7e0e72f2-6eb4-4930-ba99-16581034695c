/**********************************************************************************************************************************/
start()
var app = require('../app');
var moment = require("moment");
var fs = require('fs');
var WebSocket = require('ws')
var config = require('./../../config.json');
var configsMap = new Map();
var liveConfigsFolder = __dirname+"/../../liveConfigs/"
var contLogsFolder = __dirname+"/../../contLogs/"
var serverName = require('./../../serverName.json');
var serverSites = []
var isLambda = false
var runTriggerTime = 10
var currentTime = moment()
var socketEndPoint = 'wss://4s8atwixwf.execute-api.ap-southeast-2.amazonaws.com/Prod'
var jszip = require("jszip"); 
const startRTcalculation = require("../.././CalculationFunctions.js")
var globalSocket

var externalConfig = fs.readFileSync(__dirname+'/../.././externalConfig.json')
externalConfig = JSON.parse(externalConfig)
var runContinuous = true

var customConfig = undefined

//currentTime = moment('2023-11-08T00:01:00+08:00')

if ((parseInt(currentTime.format("HH")) == 0) && (parseInt(currentTime.format("mm")) < 10)) {
    runTriggerTime = 60*24
} else if ((parseInt(currentTime.format("hh"))%12 == 0) && (parseInt(currentTime.format("mm")) < 10)) {
    runTriggerTime = 60*12
} else if ((parseInt(currentTime.format("hh"))%6 == 0) && (parseInt(currentTime.format("mm")) < 10)) {
    runTriggerTime = 60*6
} else if ((parseInt(currentTime.format("hh"))%3 == 0) && (parseInt(currentTime.format("mm")) < 10)) {
    runTriggerTime = 60*3
} else if (parseInt(currentTime.format("mm")) < 10) {
    runTriggerTime = 60*1
} else if (parseInt(currentTime.format("mm")) < 10 || (parseInt(currentTime.format("mm")) > 30 && parseInt(currentTime.format("mm")) < 40)) {
    runTriggerTime = 60*0.5
} else {
    runTriggerTime = 10
}

//console.log("Trigger type: "+runTriggerTime+" mins")

process.argv.forEach(function (arg) {
    if (arg.includes("manConfig=")) {
        customConfig = arg.split("=")[1]
    }
});

console.log(serverName.name)

if (config[serverName.name] != undefined) { // need to create serverName.json file in root directory with {name://unique server name string//} 
    serverSites = config[serverName.name]
    isLambda = false
}

if (customConfig != undefined) {
    isLambda = false
    customConfig = JSON.parse(fs.readFileSync(__dirname+'/../../customConfigs/'+customConfig))
    serverSites = customConfig.sites
    runContinuous = customConfig.runContinuous
    externalConfig.VIP_Server_Port = customConfig.port
    if (customConfig.timerInterval != undefined) {
        externalConfig.timerInterval = customConfig.timerInterval
    }

    if (customConfig.isLambda != undefined) {
        isLambda = customConfig.isLambda
    }
}

var serverSitesNoSpace = []
for (let s of serverSites) {
    serverSitesNoSpace.push(s.replace(/\s+/g, ''))
}

var folders = fs.readdirSync(liveConfigsFolder)

folders.forEach(function (folder) {
    if (serverSites.includes(folder) || serverSitesNoSpace.includes(folder)) {
        let files = fs.readdirSync(liveConfigsFolder+folder)
        for (let file of files) {
            try {
                let obj = require(liveConfigsFolder+folder+"/"+file)
                if (obj['runTrigToPeriodCont'] != undefined) {
                    if (obj['runTrigToPeriodCont'][JSON.stringify(runTriggerTime)] != false) {
                        obj.periodForContinious = obj['runTrigToPeriodCont'][JSON.stringify(runTriggerTime)]
                        configsMap.set(obj['Site']+file.split('.')[0], obj);
                    }
                } else {
                    configsMap.set(obj['Site']+file.split('.')[0], obj);
                }
            } catch (e) {
                if (!isLambda) {
                    fs.appendFileSync(contLogsFolder+'/Error.txt', "\n "+file+" failed to load or is empty");
                }

                console.log(file+" failed to load or is empty")
            }
        }
    }
})

app.locals.configsMap = configsMap;

const { setIntervalAsync } = require('set-interval-async/dynamic');
const { clearIntervalAsync } = require('set-interval-async');

if (externalConfig.VIP_Server_Port != false) {
    app.set('port', process.env.PORT || externalConfig.VIP_Server_Port);
    var appserver = app.listen(app.get('port'), function () {
        if (!isLambda) {
            fs.appendFileSync(contLogsFolder+'/log.txt', '\n PIQ VIP calc server listening on port ' + appserver.address().port);
        }
        console.log('PIQ VIP calc server listening on port ' + appserver.address().port);
        loopThroughConfigs ()
    });
} else {
    if (!isLambda) {
        fs.appendFileSync(contLogsFolder+'/log.txt', "\n Looping Through Configs");
    }
    console.log("loopThroughConfigs")
    loopThroughConfigs ()
}

if (runContinuous) {
    calctimer = setIntervalAsync(loopThroughConfigs, externalConfig.timerInterval);
}

async function loopThroughConfigs () {
    if (!isLambda) {
        if (!fs.existsSync(contLogsFolder)) {
            fs.mkdirSync(contLogsFolder)
            fs.writeFileSync(contLogsFolder+'/Error.txt', "");
            fs.writeFileSync(contLogsFolder+'/Warning.txt', "");
            fs.writeFileSync(contLogsFolder+'/log.txt', "");
        } else {
            createTime = fs.statSync(contLogsFolder)
            if (moment().diff(moment(createTime.birthtime), 'months') > 1){
                fs.rmSync(contLogsFolder, { recursive: true, force: true });
                fs.mkdirSync(contLogsFolder)
                fs.writeFileSync(contLogsFolder+'/Error.txt', "");
                fs.writeFileSync(contLogsFolder+'/Warning.txt', "");
                fs.writeFileSync(contLogsFolder+'/log.txt', "");
                if (!isLambda) {
                    fs.appendFileSync(contLogsFolder+'/log.txt', "\n deleted old logs");
                }
                console.log("deleted old logs")
            }
        }

        for (let [k,v] of configsMap.entries()) {
            if (serverSites.includes(v.Site)) {
                let result = await startRTcalculation ({}, true, true, true, k, v) // startRTcalculation (isWriteBack, isLogReturned, isDataReturned, pageName, currentConfig)
            }
        }
    } else {
        globalSocket = new WebSocket(socketEndPoint)
        globalSocket.addEventListener('close', (event) => {
            if (!runContinuous && externalConfig.VIP_Server_Port != false) {
                appserver.close(() => {
                    end()
                    process.exit()
                })
            } else if (!runContinuous) {
                end()
            }
        });

        globalSocket.addEventListener('open', function (event) {
            let totalConfigs = configsMap.size
            for (let [k,v] of configsMap.entries()) {
                if (serverSites.includes(v.Site)) {
                    let zipFile = jszip()
                    zipFile.file("send.zip", JSON.stringify(v));
                    zipFile.generateAsync({type:"base64", compression: "DEFLATE", compressionOptions: {level: 9}}).then(async function(content) {
                        globalSocket.send(JSON.stringify({"action":"sendmessage", "message":"Config File", "data": content}), (err) => {if (err) console.log(err)})
                        console.log(k+" calc config sent to lambda server for calculation")
                        totalConfigs -= 1
                        if (totalConfigs == 0) {
                            globalSocket.close()
                        }
                    })
                }
            }
        })
    }

    if (!isLambda) {
        if (!runContinuous && externalConfig.VIP_Server_Port != false) {
            appserver.close(() => {
                end()
                process.exit()
            })
        } else if (!runContinuous) {
            end()
        }
    }
}

function start() {
    startTime = new Date();
};
  
function end() {
    endTime = new Date();
    var timeDiff = endTime - startTime; //in ms
    // strip the ms
    timeDiff /= 1000;
  
    // get seconds 
    var seconds = Math.round(timeDiff);
    if (!isLambda) {
        fs.appendFileSync(contLogsFolder+'/log.txt', "\n Closing task - Time taken = "+seconds + " seconds");
    }
    console.log("Closing task - Time taken = "+seconds + " seconds");
}